import {  InterviewCreationResponse, InterviewItem, PrepareInterviewRequest } from "@/services/interview";

class SessionStorageManager {
  /**
   * Set item in session storage
   */
  static setItem<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value);
      sessionStorage.setItem(key, serializedValue);
    } catch (error) {
      console.error(`Error setting session storage item ${key}:`, error);
    }
  }

  /**
   * Get item from session storage
   */
  static getItem<T>(key: string): T | null {
    try {
      const item = sessionStorage.getItem(key);
      if (item === null) {
        return null;
      }
      return JSON.parse(item) as T;
    } catch (error) {
      console.error(`Error getting session storage item ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove item from session storage
   */
  static removeItem(key: string): void {
    try {
      sessionStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing session storage item ${key}:`, error);
    }
  }

  /**
   * Clear all session storage
   */
  static clear(): void {
    try {
      sessionStorage.clear();
    } catch (error) {
      console.error('Error clearing session storage:', error);
    }
  }

  /**
   * Check if item exists in session storage
   */
  static hasItem(key: string): boolean {
    try {
      return sessionStorage.getItem(key) !== null;
    } catch (error) {
      console.error(`Error checking session storage item ${key}:`, error);
      return false;
    }
  }

  /**
   * Get all keys from session storage
   */
  static getAllKeys(): string[] {
    try {
      return Object.keys(sessionStorage);
    } catch (error) {
      console.error('Error getting session storage keys:', error);
      return [];
    }
  }
}

export default SessionStorageManager;

// Session storage keys constants
export const SESSION_STORAGE_KEYS = {
  INTERVIEW_SESSION: 'interview_session',
  CANDIDATE_DATA: 'candidate_data',
  INTERVIEW_ITEMS: 'interview_items',
  JOB_DATA: 'job_data',
  CHANNEL_ID: 'channel_id',
  CHAT_ID: 'chat_id',
} as const;

export const InterviewSessionStorage = {
  getCandidateData: () => SessionStorageManager.getItem(SESSION_STORAGE_KEYS.CANDIDATE_DATA),
  getInterviewItems: () => SessionStorageManager.getItem(SESSION_STORAGE_KEYS.INTERVIEW_ITEMS),
  getInterviewSession: () => SessionStorageManager.getItem(SESSION_STORAGE_KEYS.INTERVIEW_SESSION),
  getChannelId: () => SessionStorageManager.getItem<string>(SESSION_STORAGE_KEYS.CHANNEL_ID),
  getChatId: () => SessionStorageManager.getItem<string>(SESSION_STORAGE_KEYS.CHAT_ID),
  
  setCandidateData: (data: PrepareInterviewRequest) => SessionStorageManager.setItem(SESSION_STORAGE_KEYS.CANDIDATE_DATA, data),
  setInterviewItems: (items: InterviewItem[]) => SessionStorageManager.setItem(SESSION_STORAGE_KEYS.INTERVIEW_ITEMS, items),
  setInterviewSession: (session: InterviewCreationResponse) => SessionStorageManager.setItem(SESSION_STORAGE_KEYS.INTERVIEW_SESSION, session),
  setChannelId: (channelId: string) => SessionStorageManager.setItem(SESSION_STORAGE_KEYS.CHANNEL_ID, channelId),
  setChatId: (chatId: string) => SessionStorageManager.setItem(SESSION_STORAGE_KEYS.CHAT_ID, chatId),
  
  clearInterviewData: () => {
    SessionStorageManager.removeItem(SESSION_STORAGE_KEYS.CANDIDATE_DATA);
    SessionStorageManager.removeItem(SESSION_STORAGE_KEYS.INTERVIEW_ITEMS);
    SessionStorageManager.removeItem(SESSION_STORAGE_KEYS.INTERVIEW_SESSION);
    SessionStorageManager.removeItem(SESSION_STORAGE_KEYS.CHANNEL_ID);
    SessionStorageManager.removeItem(SESSION_STORAGE_KEYS.CHAT_ID);
  }
};
