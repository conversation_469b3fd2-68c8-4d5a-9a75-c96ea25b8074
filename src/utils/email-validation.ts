const PERSONAL_EMAIL_DOMAINS = [
  'gmail.com',
  'yahoo.com',
  'hotmail.com',
  'outlook.com',
  'aol.com',
  'icloud.com',
  'me.com',
  'live.com',
  'msn.com',
  'mail.com',
  'protonmail.com',
  'yandex.com',
  'zoho.com',
];

export const isBusinessEmail = (email: string): boolean => {
  const domain = email.split('@')[1]?.toLowerCase();
  return domain ? !PERSONAL_EMAIL_DOMAINS.includes(domain) : false;
};

export async function isDisposableEmail(email: string): Promise<boolean> {
  try {
    const apiUrl = `https://disposable.debounce.io/?email=${encodeURIComponent(email)}`;
    const response = await fetch(apiUrl);

    if (!response.ok) {
      console.error(`API call failed with status: ${response.status}`);
      return false;
    }

    const data = await response.json();

    return data.disposable === 'true';
  } catch (error) {
    console.error('Error checking disposable email:', error);
    return false;
  }
}
