import InterviewLayout from '@/components/layout/interview-layout';
import InterviewOnboarding from '@/features/interview/interview-onboarding';
import { createFileRoute } from '@tanstack/react-router';
import { z } from 'zod';

export const Route = createFileRoute('/(interview)/interview-onboarding/')({
  validateSearch: z.object({
    token: z.string().optional(),
  }),
  component: Component,
});

function Component() {
  return (
    <InterviewLayout>
      <InterviewOnboarding />
    </InterviewLayout>
  );
}
