import { useSearch, useNavigate } from '@tanstack/react-router';
import { useCallback, useMemo } from 'react';

interface UsePaginationProps {
  pageSize?: number;
  pageIndex?: number;
  totalItems?: number;
  pageParam?: string;
  pageSizeParam?: string;
  oneBasedUrl?: boolean;
}

export const usePagination = ({
  pageSize: defaultPageSize = 10,
  pageIndex: defaultPageIndex = 0,
  totalItems = 0,
  pageParam = 'page',
  pageSizeParam = 'pageSize',
  oneBasedUrl = true,
}: UsePaginationProps = {}) => {
  const search = useSearch({ strict: false }) as Record<string, unknown>;
  const navigate = useNavigate();

  // Get current values from URL or use defaults
  const currentPageSize = useMemo(() => {
    const urlPageSize = search[pageSizeParam];
    return urlPageSize ? parseInt(String(urlPageSize), 10) : defaultPageSize;
  }, [search, pageSizeParam, defaultPageSize]);

  const currentPageIndex = useMemo(() => {
    const urlPage = search[pageParam];
    if (!urlPage) return defaultPageIndex;

    const pageNumber = parseInt(String(urlPage), 10);
    return oneBasedUrl ? Math.max(0, pageNumber - 1) : pageNumber;
  }, [search, pageParam, defaultPageIndex, oneBasedUrl]);

  const pageCount = useMemo(() => {
    return Math.ceil(totalItems / currentPageSize);
  }, [totalItems, currentPageSize]);

  const currentPageNumber = currentPageIndex + 1;
  const isFirstPage = currentPageIndex === 0;
  const isLastPage = currentPageIndex >= pageCount - 1;

  const updateUrlParams = useCallback(
    (newPageIndex: number, newPageSize?: number) => {
      const urlPageNumber = oneBasedUrl ? newPageIndex + 1 : newPageIndex;

      const searchParams: Record<string, string | number | undefined> = {};

      if (!(urlPageNumber === (oneBasedUrl ? 1 : 0) && newPageIndex === defaultPageIndex)) {
        searchParams[pageParam] = urlPageNumber;
      }

      if (newPageSize !== undefined && newPageSize !== defaultPageSize) {
        searchParams[pageSizeParam] = newPageSize;
      }

      Object.keys(search).forEach((key) => {
        if (key !== pageParam && key !== pageSizeParam) {
          const value = search[key];
          if (typeof value === 'string' || typeof value === 'number' || value === undefined) {
            searchParams[key] = value;
          } else {
            searchParams[key] = String(value);
          }
        }
      });

      navigate({
        // Type assertion needed due to TanStack Router's strict typing
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        search: searchParams as any,
        replace: true,
      });
    },
    [search, navigate, pageParam, pageSizeParam, oneBasedUrl, defaultPageIndex, defaultPageSize]
  );

  // Setters
  const setPageIndex = useCallback(
    (pageIndex: number) => {
      const boundedPageIndex = Math.max(0, Math.min(pageIndex, pageCount - 1));
      updateUrlParams(boundedPageIndex);
    },
    [updateUrlParams, pageCount]
  );

  const setPageSize = useCallback(
    (pageSize: number) => {
      updateUrlParams(0, pageSize);
    },
    [updateUrlParams]
  );

  const changePageSize = useCallback(
    (pageSize: number) => {
      setPageSize(pageSize);
    },
    [setPageSize]
  );

  const goToPage = useCallback(
    (pageNumber: number) => {
      setPageIndex(pageNumber - 1); // Convert 1-based to 0-based
    },
    [setPageIndex]
  );

  const goToNextPage = useCallback(() => {
    if (!isLastPage) {
      setPageIndex(currentPageIndex + 1);
    }
  }, [currentPageIndex, isLastPage, setPageIndex]);

  const goToPreviousPage = useCallback(() => {
    if (!isFirstPage) {
      setPageIndex(currentPageIndex - 1);
    }
  }, [currentPageIndex, isFirstPage, setPageIndex]);

  const paginationState = useMemo(
    () => ({
      pageIndex: currentPageIndex,
      pageSize: currentPageSize,
    }),
    [currentPageIndex, currentPageSize]
  );

  return {
    // Current state
    pageIndex: currentPageIndex,
    pageSize: currentPageSize,
    currentPageNumber,
    currentPageSize,

    // Setters
    setPageIndex,
    setPageSize,
    changePageSize,

    // Navigation helpers
    goToPage,
    goToNextPage,
    goToPreviousPage,

    // Status helpers
    pageCount,
    isFirstPage,
    isLastPage,
    paginationState,
  };
};
