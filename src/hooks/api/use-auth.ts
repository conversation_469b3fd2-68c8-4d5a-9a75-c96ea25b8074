import { useAuthSlice } from '@/hooks/use-auth-slice';
import Client<PERSON>ide<PERSON>ookieManager from '@/lib/cookie';
import { login, getUserProfile, updateUserProfile, refreshTokens } from '@/services/auth';
import { getCapitalizedText } from '@/utils/helper';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'sonner';

export const authKeys = {
  all: ['auth'] as const,
  user: () => [...authKeys.all, 'user'] as const,
  profile: () => [...authKeys.user(), 'profile'] as const,
};

export function useLoginMutation() {
  const queryClient = useQueryClient();
  const { login: authLogin } = useAuthSlice();

  return useMutation({
    mutationKey: ['login'],
    mutationFn: async (payload: ILoginRequest) => {
      const response = await login(payload);
      if (response.data.data) {
        const { access_token, refresh_token, user } = response.data.data;
        authLogin(access_token, refresh_token || '', user);
        if (user) {
          queryClient.setQueryData(authKeys.profile(), user);
        }
      }
      return {
        access_token: response.data.data.access_token,
        refresh_token: response.data.data.refresh_token,
        user: response.data.data.user,
        message: response.data.message,
      };
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || '';
        if (errorMessage.includes('record not found')) {
          toast.error('No account found matching those credentials.');
        } else {
          toast.error(getCapitalizedText(errorMessage) || 'Login failed');
        }
      }
    },
  });
}

export function useRefreshTokenMutation() {
  const { setTokens, logout } = useAuthSlice();

  return useMutation({
    mutationKey: ['refresh-token'],
    mutationFn: async () => {
      const refreshToken = ClientSideCookieManager.getClientCookie(
        import.meta.env.VITE_PREVIA_ADMIN_REFRESH_TOKEN_KEY!
      );
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await refreshTokens(refreshToken);
      if (response.data.data) {
        const { access_token, refresh_token } = response.data.data;
        setTokens(access_token, refresh_token || '');
      }
      return response;
    },
    onError: (error: Error) => {
      logout();

      if (error instanceof AxiosError) {
        toast.error('Session expired. Please log in again.');
      }
    },
  });
}

export function useUserProfileQuery(options = { enabled: true }) {
  const { setUser, isAuthenticated } = useAuthSlice();
  const refreshTokenMutation = useRefreshTokenMutation();

  return useQuery({
    queryKey: authKeys.profile(),
    queryFn: async () => {
      try {
        const response = await getUserProfile();
        const user = response.data?.data?.user;
        if (user) setUser(user);
        return user;
      } catch (error) {
        if (error instanceof AxiosError && error.response?.status === 401) {
          // If unauthorized, try to refresh the token
          await refreshTokenMutation.mutateAsync();
          const retryResponse = await getUserProfile();
          const retryUser = retryResponse.data?.data?.user;
          if (retryUser) setUser(retryUser);
          return retryUser;
        }
        throw error;
      }
    },
    enabled: options.enabled && isAuthenticated(),
    staleTime: 5 * 60 * 1000,
  });
}

export function useUpdateProfileMutation() {
  const queryClient = useQueryClient();
  const { setUser } = useAuthSlice();

  return useMutation({
    mutationKey: ['update-profile'],
    mutationFn: (payload: Partial<IUser>) => updateUserProfile(payload),
    onSuccess: (data) => {
      setUser(data.data);
      queryClient.setQueryData(authKeys.profile(), data.data);
      toast.success('Profile updated successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        toast.error(error.response?.data?.message || 'Update failed');
      }
    },
  });
}

export function useLogoutMutation() {
  const queryClient = useQueryClient();
  const { logout } = useAuthSlice();

  return useMutation({
    mutationKey: ['logout'],
    mutationFn: async () => {
      logout();
      queryClient.clear();
    },
  });
}

export function useLogout() {
  const logoutMutation = useLogoutMutation();

  return () => {
    logoutMutation.mutate();
  };
}
