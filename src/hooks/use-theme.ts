import { useStore } from '@/stores/store';
import { useEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';

export const useTheme = () => {
  const theme = useStore(useShallow((state) => state.theme));
  const setTheme = useStore(useShallow((state) => state.setTheme));

  useEffect(() => {
    // Initialize theme from localStorage on mount
    const savedTheme = localStorage.getItem(
      import.meta.env.VITE_UI_THEME || 'previa-ui-theme'
    ) as Theme | null;
    const initialTheme = savedTheme || 'light';

    if (theme !== initialTheme) {
      setTheme(initialTheme);
    }

    // Apply initial theme
    const root = window.document.documentElement;
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const applyTheme = (currentTheme: Theme) => {
      root.classList.remove('light', 'dark');
      const systemTheme = mediaQuery.matches ? 'dark' : 'light';
      const effectiveTheme = currentTheme === 'system' ? systemTheme : currentTheme;
      root.classList.add(effectiveTheme);
    };

    const handleSystemThemeChange = () => {
      if (theme === 'system') {
        applyTheme('system');
      }
    };

    applyTheme(theme);
    mediaQuery.addEventListener('change', handleSystemThemeChange);

    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, [theme, setTheme]);

  return {
    theme,
    setTheme,
  };
};
