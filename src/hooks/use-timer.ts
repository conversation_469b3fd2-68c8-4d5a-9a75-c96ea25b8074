import { useState, useEffect, useRef, useCallback } from 'react';

interface Props {
  initialTime: number; // in seconds
}

interface UseTimerResult {
  timeLeft: number;
  isTimerActive: boolean;
  startTimer: () => void;
  clearTimer: () => void;
}

export function useTimer({ initialTime }: Props): UseTimerResult {
  const [timeLeft, setTimeLeft] = useState(initialTime);
  const [isTimerActive, setIsTimerActive] = useState(true);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const startTimer = useCallback(() => {
    setTimeLeft(initialTime);
    setIsTimerActive(true);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    timerRef.current = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timerRef.current!);
          setIsTimerActive(false);
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);
  }, [initialTime]);

  const clearTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    setIsTimerActive(false);
  }, []);

  useEffect(() => {
    startTimer();

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [startTimer]);

  return { timeLeft, isTimerActive, startTimer, clearTimer };
}
