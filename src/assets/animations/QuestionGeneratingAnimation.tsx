import animationData from '../../../public/lottie/document-loader.json';
import Lottie from 'react-lottie';

const QuestionGeneratingAnimation = () => {
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };
  return (
    <div>
      <Lottie options={defaultOptions} width={175} height={175} />
    </div>
  );
};

export default QuestionGeneratingAnimation;
