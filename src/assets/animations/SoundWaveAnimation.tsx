import animationData from '../../../public/lottie/sound-wave.json';
import <PERSON><PERSON> from 'react-lottie';

const SoundWaveAnimation = () => {
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };
  return (
    <div>
      <Lottie options={defaultOptions} width={350} height={175} />
    </div>
  );
};

export default SoundWaveAnimation;
