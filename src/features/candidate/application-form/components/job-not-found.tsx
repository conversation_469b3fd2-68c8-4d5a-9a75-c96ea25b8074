'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Link } from '@tanstack/react-router';
import { Search } from 'lucide-react';

export function JobNotFound() {
  return (
    <div className='flex min-h-screen items-center justify-center p-4'>
      <div className='w-full max-w-md text-center'>
        {/* Simple Icon */}
        <div className='mb-6 flex justify-center'>
          <div className='flex h-16 w-16 items-center justify-center rounded-full bg-gray-100'>
            <Search className='h-8 w-8 text-gray-400' />
          </div>
        </div>

        <h1 className='text-gray-dark mb-3 text-2xl font-semibold'>Job not found</h1>

        <p className='text-gray-light mb-8'>
          This job posting you are looking for is no longer available or does not exist.
        </p>

        <Link to='/'>
          <Button className='w-full'>Browse all jobs</Button>
        </Link>
      </div>
    </div>
  );
}
