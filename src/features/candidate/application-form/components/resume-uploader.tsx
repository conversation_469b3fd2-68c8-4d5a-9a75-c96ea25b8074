import FileUploader from '@/components/file-uploader/FileUploader';
import { API_ROUTES } from '@/config/api';
import { ApiServiceInstance } from '@/services';
import { type AxiosError } from 'axios';
import { X } from 'lucide-react';
import React, { useState } from 'react';
import { toast } from 'sonner';

interface Props {
  jobId: string;
  cv: string | null;
  setCV: React.Dispatch<React.SetStateAction<string | null>>;
}

const ResumeUploader = ({ jobId, cv, setCV }: Props) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleUploadCV = async (files: File[]) => {
    if (files.length === 0) {
      toast.error('Please select a file to upload');
      return;
    }

    setIsLoading(true);
    try {
      const formData = new FormData();
      formData.append('cv', files[0]);

      const response: IResponseData<{ cv_link: string }> = await ApiServiceInstance.callPostApi<
        { cv_link: string },
        FormData
      >(API_ROUTES.CANDIDATE.UPLOAD_CV(jobId), formData, null, 'multipart/form-data');

      setCV(response.data?.cv_link);
      toast.success('CV uploaded successfully');
    } catch (err) {
      toast.error(
        (err as AxiosError<{ message: string }>)?.response?.data?.message ?? 'Failed to upload cv'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveCV = () => {
    setCV(null);
  };

  return (
    <div className='relative'>
      {cv && (
        <div
          className='absolute -top-3 -right-3 z-10 cursor-pointer rounded-lg border bg-white p-1.5 shadow'
          onClick={handleRemoveCV}
        >
          <X className='size-5 text-red-700' />
        </div>
      )}
      <FileUploader
        placeholder='Drag your file or'
        placeHolder2='browse'
        supportedFormats='Max 2 MB files are allowed'
        acceptedFileTypes={{ 'application/pdf': ['.pdf'], 'application/msword': ['.doc'] }}
        onFilesUploaded={(files) => handleUploadCV(files)}
        isLoading={isLoading}
        clearFiles={!isLoading && cv === null}
        maxFileSize={2 * 1024 * 1024} // 2 MB
      />
    </div>
  );
};

export default ResumeUploader;
