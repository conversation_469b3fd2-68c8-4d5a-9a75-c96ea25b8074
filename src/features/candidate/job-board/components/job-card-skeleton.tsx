import { Card, CardContent } from '@/components/ui/card';

export function JobCardSkeleton() {
  return (
    <>
      {[...Array(3)].map((_, index) => (
        <Card className='animate-pulse rounded-2xl' key={index}>
          <CardContent className='p-8'>
            <div className='flex flex-col items-start justify-start gap-4 sm:flex-row sm:items-center sm:justify-between'>
              <div className='flex w-full flex-1 items-start gap-4 sm:flex-row sm:items-center sm:gap-6'>
                <div className='h-16 w-16 flex-shrink-0 rounded-2xl bg-slate-200'></div>

                <div className='min-w-0 flex-1'>
                  <div className='mb-3 flex flex-col items-start gap-2 sm:mb-5 sm:flex-row sm:items-center sm:gap-4'>
                    <div className='h-8 w-full rounded-lg bg-slate-200 sm:w-48'></div>
                    <div className='h-7 w-24 rounded-full bg-slate-200'></div>
                  </div>

                  <div className='flex flex-wrap items-center gap-4 sm:gap-6'>
                    <div className='flex items-center gap-2.5 rounded-xl border border-slate-100 bg-slate-50 px-4 py-2.5'>
                      <div className='h-5 w-5 rounded-full bg-slate-200'></div>
                      <div className='h-4 w-24 rounded bg-slate-200'></div>
                    </div>
                    <div className='flex items-center gap-2.5 rounded-xl border border-slate-100 bg-slate-50 px-4 py-2.5'>
                      <div className='h-5 w-5 rounded-full bg-slate-200'></div>
                      <div className='h-4 w-28 rounded bg-slate-200'></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className='flex w-full items-center sm:w-auto'>
                <div className='h-12 w-32 rounded-xl bg-slate-200'></div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </>
  );
}
