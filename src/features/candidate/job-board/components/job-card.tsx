import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CardContent } from '@/components/ui';
import { useNavigate } from '@tanstack/react-router';
import { Building2, MapPin, Clock, ArrowRight } from 'lucide-react';

export const JobCard = ({ job }: { job: ActiveJob }) => {
  const navigate = useNavigate();

  function getExperienceLevel(maxExp: number): {
    level: string;
    color: string;
  } {
    if (maxExp <= 2)
      return {
        level: 'Entry Level',
        color: 'bg-green-100 text-green-800 border-green-200',
      };
    if (maxExp <= 5)
      return {
        level: 'Mid Level',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
      };
    return {
      level: 'Senior Level',
      color: 'bg-purple-100 text-purple-800 border-purple-200',
    };
  }
  const { level, color } = getExperienceLevel(job.max_exp);
  return (
    <Card>
      <CardContent className='px-8 py-2 md:py-6'>
        <div className='flex flex-col justify-between md:flex-row md:items-center'>
          <div className='flex flex-1 gap-6 md:items-center'>
            <div className='from-primary-500 to-primary-600 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br'>
              <Building2 className='h-8 w-8 text-white/80' />
            </div>

            <div className='min-w-0 flex-1'>
              <div className='mb-5 flex flex-col gap-4 sm:flex-row sm:items-center'>
                <h3 className='text-2xl leading-tight font-medium text-black'>{job.title}</h3>
                <Badge
                  className={`flex items-center gap-1 rounded-full border px-3 py-0.5 text-sm font-medium ${color}`}
                >
                  {level}
                </Badge>
              </div>

              <div className='flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-6'>
                <div className='flex items-center gap-2.5 rounded-xl border border-slate-100 bg-slate-50 px-4 py-2'>
                  <div className='flex h-5 w-5 items-center justify-center rounded-full bg-slate-200'>
                    <MapPin className='text-gray-dark h-3 w-3' />
                  </div>
                  <span className='text-gray-dark text-sm font-medium'>{job.location}</span>
                </div>
                <div className='flex items-center gap-2.5 rounded-xl border border-slate-100 bg-slate-50 px-4 py-2'>
                  <div className='flex h-5 w-5 items-center justify-center rounded-full bg-slate-200'>
                    <Clock className='text-gray-dark h-3 w-3' />
                  </div>
                  <span className='text-gray-dark text-sm font-medium'>
                    {job.min_exp === job.max_exp
                      ? `${job.min_exp} year${job.min_exp !== 1 ? 's' : ''} experience`
                      : `${job.min_exp}-${job.max_exp} years experience`}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className='mt-6 flex items-center md:mt-0'>
            <Button
              className='from-primary-500 to-primary-600 w-full rounded-xl bg-gradient-to-bl !px-4 sm:w-fit'
              onClick={() => navigate({ to: '/job-application/$jobId', params: { jobId: job.id } })}
            >
              <span>Apply Now</span>
              <ArrowRight className='ml-2 h-4 w-4' />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
