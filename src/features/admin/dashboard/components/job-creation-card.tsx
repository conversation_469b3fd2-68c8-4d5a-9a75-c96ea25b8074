'use client';

import { GradientCircle } from '@/components/shared/GradientCircle';
import { <PERSON><PERSON>, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';

const jobPositions = [
  'Frontend Developer',
  'HR Specialist',
  'UI/UX Designer',
  'QA Engineer',
  'Data Analyst',
];

export function JobCreationCard() {
  const navigate = useNavigate();
  return (
    <Card className='border-previa-neutral-200 relative z-10 flex h-full flex-col overflow-hidden shadow-md'>
      <GradientCircle className='-top-64 -right-72' />{' '}
      <CardHeader className='z-50'>
        <CardTitle className='text-xl font-normal'>
          Let&apos;s Create a{' '}
          <span className='leading-relaxed font-semibold'>New Job Post with AI</span>
        </CardTitle>
        <CardDescription className='text-muted-foreground max-w-sm'>
          Choose a position or add your own job details, let
          <span className='font-semibold'> AI </span>
          handle the rest. Prefer full control? Create manually instead.
        </CardDescription>
      </CardHeader>
      <CardContent className='z-50 flex flex-1 flex-col items-start justify-between space-y-6'>
        <div className='flex flex-wrap gap-2 self-stretch'>
          {jobPositions.map((position, index) => (
            <motion.div
              key={position}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Button
                variant='outline'
                size='sm'
                className='bg-previa-neutral-50 hover:border-primary-300 hover:bg-primary-100 rounded-full text-gray-700'
              >
                {position}
              </Button>
            </motion.div>
          ))}
          <Button
            variant='outline'
            size='sm'
            className='bg-previa-neutral-50 text-muted-foreground hover:border-primary-300 hover:bg-primary-100 rounded-full'
          >
            View all
          </Button>
        </div>

        <div className='flex w-full gap-3'>
          <Button
            className='bg-primary hover:bg-primary-700 w-full font-medium text-white'
            onClick={() => {
              navigate({ to: '/admin/jobs/create' });
            }}
          >
            Create a New Job Post
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
