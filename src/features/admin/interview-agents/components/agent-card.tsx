"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { MoreVertical, Clock, Briefcase, Target, User } from "lucide-react"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { getInitials } from "@/utils/helper"

const difficultyColors = {
  Easy: "bg-green-100 text-green-800 hover:bg-green-200",
  Medium: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
  Hard: "bg-red-100 text-red-800 hover:bg-red-200",
}

export function AgentCard({ agent, onEdit, onDelete, onDuplicate, onViewDetails }: AgentCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className="h-full cursor-pointer transition-all duration-200 hover:shadow-lg border-border/50">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={agent.avatar || "/placeholder.svg"} alt={agent.name} />
                <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                  {getInitials(agent.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-lg text-foreground truncate">{agent.name}</h3>
                <p className="text-sm text-muted-foreground flex items-center mt-1">
                  <Briefcase className="h-3 w-3 mr-1" />
                  {agent.role}
                </p>
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => onViewDetails?.(agent)}>
                  <User className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onEdit?.(agent)}>Edit Agent</DropdownMenuItem>
                <DropdownMenuItem onClick={() => onDuplicate?.(agent)}>Duplicate</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDelete?.(agent.id)}
                  className="text-destructive focus:text-destructive"
                >
                  Delete Agent
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-4">
            {/* Stats Row */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-4">
                <div className="flex items-center text-muted-foreground">
                  <Target className="h-3 w-3 mr-1" />
                  <span className="font-medium">{agent.jobsCount}</span>
                  <span className="ml-1">jobs</span>
                </div>
                <div className="flex items-center text-muted-foreground">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{agent.duration}min</span>
                </div>
              </div>
              <Badge variant="secondary" className={difficultyColors[agent.difficulty]}>
                {agent.difficulty}
              </Badge>
            </div>

            {/* Persona Description */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Persona</span>
                <div className="flex-1 h-px bg-border"></div>
              </div>
              <p className="text-sm text-muted-foreground leading-relaxed line-clamp-3">{agent.persona}</p>
            </div>

            {/* Action Button */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: isHovered ? 1 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                variant="outline"
                size="sm"
                className="w-full mt-2 bg-transparent"
                onClick={() => onViewDetails?.(agent)}
              >
                See Agent Details
              </Button>
            </motion.div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
