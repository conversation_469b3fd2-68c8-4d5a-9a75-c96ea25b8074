'use client';

import { AgentCard } from './agent-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { motion } from 'framer-motion';
import { Plus, Search, Filter, Grid3X3, List } from 'lucide-react';
import { useState } from 'react';

interface AgentsListProps {
  agents: InterviewAgent[];
  onCreateAgent?: () => void;
  onEditAgent?: (agent: InterviewAgent) => void;
  onDeleteAgent?: (agentId: string) => void;
  onDuplicateAgent?: (agent: InterviewAgent) => void;
  onViewAgentDetails?: (agent: InterviewAgent) => void;
}

export function AgentsList({
  agents,
  onCreateAgent,
  onEditAgent,
  onDeleteAgent,
  onDuplicateAgent,
  onViewAgentDetails,
}: AgentsListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [difficultyFilter, setDifficultyFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const filteredAgents = agents.filter((agent) => {
    const matchesSearch =
      agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      agent.role.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesDifficulty =
      difficultyFilter === 'all' || agent.difficulty.toLowerCase() === difficultyFilter;

    return matchesSearch && matchesDifficulty;
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <div className='space-y-6'>
      {/* Filters and Search */}
      <div className='flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center'>
        <div className='flex flex-1 flex-col gap-3 sm:flex-row'>
          <div className='relative max-w-md flex-1'>
            <Search className='text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform' />
            <Input
              placeholder='Search agents by name or role...'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className='pl-10'
            />
          </div>

          <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
            <SelectTrigger className='w-full sm:w-[180px]'>
              <Filter className='mr-2 h-4 w-4' />
              <SelectValue placeholder='Difficulty' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Difficulties</SelectItem>
              <SelectItem value='easy'>Easy</SelectItem>
              <SelectItem value='medium'>Medium</SelectItem>
              <SelectItem value='hard'>Hard</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className='flex items-center gap-2'>
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size='sm'
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className='h-4 w-4' />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size='sm'
            onClick={() => setViewMode('list')}
          >
            <List className='h-4 w-4' />
          </Button>
        </div>
        <Button onClick={onCreateAgent} className='shrink-0'>
          <Plus className='mr-2 h-4 w-4' />
          Create Agent
        </Button>
      </div>

      {/* Results Count */}
      <div className='text-muted-foreground text-sm'>
        Showing {filteredAgents.length} of {agents.length} agents
      </div>

      {/* Agents Grid */}
      {filteredAgents.length > 0 ? (
        <motion.div
          variants={containerVariants}
          initial='hidden'
          animate='visible'
          className={`grid gap-6 ${
            viewMode === 'grid'
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1'
          }`}
        >
          {filteredAgents.map((agent) => (
            <AgentCard
              key={agent.id}
              agent={agent}
              onEdit={onEditAgent}
              onDelete={onDeleteAgent}
              onDuplicate={onDuplicateAgent}
              onViewDetails={onViewAgentDetails}
            />
          ))}
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className='py-12 text-center'
        >
          <div className='text-muted-foreground'>
            <Search className='mx-auto mb-4 h-12 w-12 opacity-50' />
            <h3 className='mb-2 text-lg font-medium'>No agents found</h3>
            <p className='text-sm'>
              {searchQuery || difficultyFilter !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Create your first interview agent to get started'}
            </p>
          </div>
          {!searchQuery && difficultyFilter === 'all' && (
            <Button onClick={onCreateAgent} className='mt-4'>
              <Plus className='mr-2 h-4 w-4' />
              Create Your First Agent
            </Button>
          )}
        </motion.div>
      )}
    </div>
  );
}
