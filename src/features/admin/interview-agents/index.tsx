import { <PERSON>Header } from '@/components/shared/PageHeader';
import { AgentsList } from './components/agents-list';
import { Main } from '@/components/layout/main';

// Mock data for demonstration
const mockAgents: InterviewAgent[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'Senior Frontend Developer',
    difficulty: 'Hard',
    duration: 45,
    jobsCount: 23,
    persona:
      'A seasoned frontend developer with expertise in React, TypeScript, and modern web technologies. Known for asking detailed questions about component architecture, state management, and performance optimization.',
    createdAt: new Date('2024-01-15'),
    lastUsed: new Date('2024-01-20'),
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'Product Manager',
    difficulty: 'Medium',
    duration: 30,
    jobsCount: 18,
    persona:
      'An experienced product manager who focuses on strategic thinking, user empathy, and data-driven decision making. Evaluates candidates on their ability to prioritize features and communicate with stakeholders.',
    createdAt: new Date('2024-01-10'),
    lastUsed: new Date('2024-01-19'),
  },
  {
    id: '3',
    name: 'Dr<PERSON> <PERSON>',
    role: 'Data Scientist',
    difficulty: 'Hard',
    duration: 60,
    jobsCount: 31,
    persona:
      'A PhD in Statistics with 8+ years in machine learning. Specializes in evaluating statistical knowledge, model selection, and the ability to explain complex concepts to non-technical stakeholders.',
    createdAt: new Date('2024-01-08'),
    lastUsed: new Date('2024-01-21'),
  },
  {
    id: '4',
    name: 'Alex Thompson',
    role: 'Junior Developer',
    difficulty: 'Easy',
    duration: 25,
    jobsCount: 12,
    persona:
      'A friendly and encouraging interviewer perfect for entry-level positions. Focuses on fundamental programming concepts, problem-solving approach, and learning potential rather than advanced technical skills.',
    createdAt: new Date('2024-01-12'),
    lastUsed: new Date('2024-01-18'),
  },
  {
    id: '5',
    name: 'Rachel Kim',
    role: 'UX Designer',
    difficulty: 'Medium',
    duration: 40,
    jobsCount: 27,
    persona:
      'A creative UX designer with a strong background in user research and design systems. Evaluates design thinking, user empathy, and the ability to justify design decisions with data and user feedback.',
    createdAt: new Date('2024-01-05'),
    lastUsed: new Date('2024-01-22'),
  },
  {
    id: '6',
    name: 'David Park',
    role: 'DevOps Engineer',
    difficulty: 'Hard',
    duration: 50,
    jobsCount: 19,
    persona:
      'A systems-focused engineer with expertise in cloud infrastructure, CI/CD, and monitoring. Challenges candidates on scalability, reliability, and their understanding of modern deployment practices.',
    createdAt: new Date('2024-01-03'),
    lastUsed: new Date('2024-01-17'),
  },
];

export default function InterviewAgentsList() {
  const handleCreateAgent = () => {
    console.log('Create new agent');
  };

  const handleEditAgent = (agent: InterviewAgent) => {
    console.log('Edit agent:', agent.name);
  };

  const handleDeleteAgent = (agentId: string) => {
    console.log('Delete agent:', agentId);
  };

  const handleDuplicateAgent = (agent: InterviewAgent) => {
    console.log('Duplicate agent:', agent.name);
  };

  const handleViewAgentDetails = (agent: InterviewAgent) => {
    console.log('View agent details:', agent.name);
  };

  return (
    <div className='dark:bg-background flex h-full flex-col bg-neutral-100'>
      <PageHeader title='Interview Agents' />

      <Main>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1'>
          <AgentsList
            agents={mockAgents}
            onCreateAgent={handleCreateAgent}
            onEditAgent={handleEditAgent}
            onDeleteAgent={handleDeleteAgent}
            onDuplicateAgent={handleDuplicateAgent}
            onViewAgentDetails={handleViewAgentDetails}
          />
        </div>
      </Main>
    </div>
  );
}
