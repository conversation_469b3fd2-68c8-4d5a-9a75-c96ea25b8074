import { paymentColumns, payments } from './data';
import {
  DataTable,
  DataTableContainer,
  DataTableFooter,
  DataTableHeader,
} from '@/components/data-table';
import { SearchInput } from '@/components/shared/search-input';
import TablePagination from '@/components/shared/table-pagination';
import { Button } from '@/components/ui';
import { useNavigate } from '@tanstack/react-router';
import { useMemo } from 'react';

export default function JobList() {
  const navigate = useNavigate();

  const data = useMemo(() => payments, []);
  const columns = useMemo(() => paymentColumns, []);

  return (
    <div className='bg-custom-white min-h-screen p-6'>
      <h3 className='text-gray-dark pb-6 text-2xl'>Job List</h3>
      <DataTableContainer>
        <DataTableHeader>
          <DataTableHeader.Left>
            <SearchInput />
          </DataTableHeader.Left>
          <DataTableHeader.Right>
            <Button>Add new job post</Button>
          </DataTableHeader.Right>
        </DataTableHeader>
        <DataTable
          data={data}
          columns={columns}
          onRowClick={(row) =>
            navigate({ from: '/admin/jobs', to: '/admin/jobs/$jobId', params: { jobId: row.id } })
          }
        />
        <DataTableFooter>
          <TablePagination totalRecords={12} />
        </DataTableFooter>
      </DataTableContainer>
    </div>
  );
}
