import { Badge } from '@/components/ui';
import { ColumnDef } from '@tanstack/react-table';

export type Payment = {
  id: string;
  amount: number;
  status: 'pending' | 'processing' | 'success' | 'failed';
  email: string;
};

export const paymentColumns: ColumnDef<Payment>[] = [
  {
    accessorKey: 'email',
    header: 'Email',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const getBadgeVariant = (status: string) => {
        switch (status) {
          case 'pending':
            return 'secondary';
          case 'processing':
            return 'default';
          case 'success':
            return 'success';
          case 'failed':
            return 'destructive';
          default:
            return 'secondary';
        }
      };

      return (
        <Badge
          key={row.original.id}
          variant={getBadgeVariant(row.original.status)}
          className='capitalize'
        >
          {row.original.status}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'amount',
    header: 'Amount',
  },
];

export const payments: Payment[] = [
  {
    id: '728ed52f3',
    amount: 100,
    status: 'pending',
    email: '<EMAIL>',
  },
  {
    id: '489e1d42',
    amount: 125,
    status: 'processing',
    email: '<EMAIL>',
  },
  {
    id: '489e1d422',
    amount: 125,
    status: 'success',
    email: '<EMAIL>',
  },
  {
    id: '489e1d421',
    amount: 125,
    status: 'failed',
    email: '<EMAIL>',
  },
  {
    id: '489e1d424',
    amount: 125,
    status: 'pending',
    email: '<EMAIL>',
  },
];
