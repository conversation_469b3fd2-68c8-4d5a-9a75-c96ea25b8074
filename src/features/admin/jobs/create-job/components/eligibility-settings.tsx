'use client';

import { Switch, Label, Textarea } from '@/components/ui';
import { Info } from 'lucide-react';
import { useState } from 'react';

interface EligibilitySettingsProps {
  onEligibilityChange?: (data: {
    enabled: boolean;
    inclusiveRequirements: string;
    exclusiveRequirements: string;
  }) => void;
}

export function EligibilitySettings({ onEligibilityChange }: EligibilitySettingsProps) {
  const [eligibilityEnabled, setEligibilityEnabled] = useState(false);
  const [inclusiveRequirements, setInclusiveRequirements] = useState('');
  const [exclusiveRequirements, setExclusiveRequirements] = useState('');

  const handleToggleChange = (enabled: boolean) => {
    setEligibilityEnabled(enabled);
    onEligibilityChange?.({
      enabled,
      inclusiveRequirements,
      exclusiveRequirements,
    });
  };

  const handleInclusiveChange = (value: string) => {
    setInclusiveRequirements(value);
    onEligibilityChange?.({
      enabled: eligibilityEnabled,
      inclusiveRequirements: value,
      exclusiveRequirements,
    });
  };

  const handleExclusiveChange = (value: string) => {
    setExclusiveRequirements(value);
    onEligibilityChange?.({
      enabled: eligibilityEnabled,
      inclusiveRequirements,
      exclusiveRequirements: value,
    });
  };

  return (
    <div className='space-y-4'>
      {/* Toggle Section */}
      <div className='bg-muted/30 flex items-center justify-between rounded-lg p-4'>
        <div className='flex items-center gap-2'>
          <Label htmlFor='eligibility-toggle' className='text-sm font-medium'>
            Want to set Candidate Eligibility Settings
          </Label>
          <Info className='text-muted-foreground size-4 cursor-pointer' />
        </div>
        <Switch
          id='eligibility-toggle'
          checked={eligibilityEnabled}
          onCheckedChange={handleToggleChange}
        />
      </div>

      {/* Eligibility Requirements Section */}
      {eligibilityEnabled && (
        <div className='animate-in slide-in-from-top-2 space-y-4 duration-200'>
          <div className='space-y-2'>
            <Label htmlFor='inclusive-requirements' className='text-foreground text-sm font-medium'>
              Inclusive Requirements
            </Label>
            <Textarea
              id='inclusive-requirements'
              placeholder='Enter requirements that candidates must have...'
              value={inclusiveRequirements}
              onChange={(e) => handleInclusiveChange(e.target.value)}
              className='min-h-[80px] resize-none'
            />
          </div>

          <div className='space-y-2'>
            <Label htmlFor='exclusive-requirements' className='text-foreground text-sm font-medium'>
              Exclusive Requirements
            </Label>
            <Textarea
              id='exclusive-requirements'
              placeholder='Enter requirements that would disqualify candidates...'
              value={exclusiveRequirements}
              onChange={(e) => handleExclusiveChange(e.target.value)}
              className='min-h-[80px] resize-none'
            />
          </div>
        </div>
      )}
    </div>
  );
}
