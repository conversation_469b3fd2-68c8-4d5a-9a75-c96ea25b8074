'use client';

import { RobotIcon } from '@/assets/icons';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AnimatePresence, Reorder, useDragControls } from 'framer-motion';
import { Hourglass, MoreHorizontal, SquarePen, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface ListItem {
  id: string;
  title: string;
  duration: string;
  icon: string;
}

interface DraggableListProps {
  initialItems: ListItem[];
}

export function DraggablePersonaList({ initialItems }: DraggableListProps) {
  const [items, setItems] = useState(initialItems);

  return (
    <Reorder.Group axis='y' values={items} onReorder={setItems} className='space-y-6'>
      <AnimatePresence>
        {items.map((item) => (
          <DraggablePersonaCard key={item.id} item={item} />
        ))}
      </AnimatePresence>
    </Reorder.Group>
  );
}

function DraggablePersonaCard({ item }: { item: ListItem }) {
  const dragControls = useDragControls();

  return (
    <Reorder.Item
      value={item}
      className='border-strock rounded-2xl border bg-white p-6'
      dragListener={false}
      dragControls={dragControls}
    >
      <div className='flex items-center gap-2'>
        <div
          className='-ml-3 cursor-grab touch-none rounded-lg p-3 transition-colors select-none hover:bg-gray-50 active:cursor-grabbing'
          onPointerDown={(e) => {
            dragControls.start(e);
          }}
          onPointerUp={(e) => {
            e.currentTarget.releasePointerCapture(e.pointerId);
          }}
          style={{ touchAction: 'none' }}
        >
          <div className='grid grid-cols-2 gap-1'>
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className='bg-gray-light h-1 w-1 rounded-full transition-colors group-hover:bg-gray-500'
              />
            ))}
          </div>
        </div>

        <div className='bg-custom-white mr-2 rounded-md p-3'>
          <RobotIcon className='text-gray-dark' />
        </div>

        <div className='flex-1'>
          <h3 className='mb-1 text-lg font-semibold text-black'>{item.title}</h3>
          <p className='text-gray-light text-sm'>
            Interview Duration: <span className='text-gray-dark font-medium'>{item.duration}</span>
          </p>
        </div>

        {/* Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
              <MoreHorizontal className='text-gray-dark h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='text-gray-dark'>
            <DropdownMenuItem>
              <Hourglass className='text-gray-dark size-4' />
              <p>Update Duration</p>
            </DropdownMenuItem>
            <DropdownMenuItem disabled>
              <SquarePen className='text-gray-dark size-4' />
              <p>Edit Agent</p>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Trash2 className='text-gray-dark size-4' />
              <p>Remove</p>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </Reorder.Item>
  );
}
