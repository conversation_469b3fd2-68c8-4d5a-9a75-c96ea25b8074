'use client';

import { DraggablePersonaList } from './draggable-persona-list';
import { AsyncSelector } from '@/components/select/async-selector';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui';
import { useJobStore } from '@/features/admin/jobs/create-job/hooks/useJobStore';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import { forwardRef, useImperativeHandle, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const interviewSchema = z.object({
  interviewAgent: z.string().min(1, 'Interview agent is required'),
  interviewDuration: z.string().min(1, 'Interview duration is required'),
});

export const InterviewAgentStep = forwardRef<{ triggerValidation: () => Promise<boolean> }>(
  (_, ref) => {
    const { formData, updateFormData } = useJobStore();

    const form = useForm({
      resolver: zodResolver(interviewSchema),
      defaultValues: {
        interviewAgent: formData.interviewAgent,
        interviewDuration: formData.interviewDuration,
      },
    });

    useImperativeHandle(ref, () => ({
      triggerValidation: async () => {
        return await form.trigger();
      },
    }));

    const handleAdd = () => {
      updateFormData({
        totalAgents: formData.totalAgents + 1,
        totalDuration: '30m',
      });
    };

    const [selectedUser, setSelectedUser] = useState('');

    const initialItems = [
      {
        id: '1',
        title: 'HR Introduction',
        duration: '20m',
        icon: '🤖',
      },
      {
        id: '2',
        title: 'Python developer',
        duration: '50m',
        icon: '🤖',
      },
      {
        id: '3',
        title: 'React developer',
        duration: '50m',
        icon: '🤖',
      },
      {
        id: '4',
        title: 'UX designer',
        duration: '50m',
        icon: '🤖',
      },
    ];

    return (
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className='mx-auto max-w-2xl'
      >
        <Card className='border-none bg-transparent'>
          <CardHeader className='text-center'>
            <CardTitle className='text-xl'>Select Your AI Interview Personas</CardTitle>
            <CardDescription>
              Choose AI personas best suited for this role. If you don&apos;t find a perfect match,
              you can create a new custom persona tailored to your needs.
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            {/* Stats */}
            <div className='grid grid-cols-2 rounded-lg bg-white p-4'>
              <div className='text-left'>
                <div className='text-gray-light mb-1 text-sm'>Total Interview Persona</div>
                <div className='font-medium text-black'>{formData.totalAgents}</div>
              </div>
              <div className='text-left'>
                <div className='text-gray-light mb-1 text-sm'>Total Duration</div>
                <div className='font-medium text-black'>{formData.totalDuration}</div>
              </div>
            </div>

            {/* Draggable Persona List */}
            <DraggablePersonaList initialItems={initialItems} />

            <div className='border-strock space-y-6 rounded-2xl border bg-white p-10'>
              <div className='space-y-3'>
                <Label htmlFor='interviewAgent'>Interview Persona</Label>
                <AsyncSelector
                  options={[
                    { value: '1', label: 'John Doe' },
                    { value: '2', label: 'Jane Smith' },
                    { value: '3', label: 'Bob Johnson' },
                    { value: '4', label: 'Alice Brown' },
                    { value: '5', label: 'Charlie Wilson' },
                    { value: '6', label: 'Diana Davis' },
                    { value: '7', label: 'Edward Miller' },
                    { value: '8', label: 'Fiona Garcia' },
                  ]}
                  value={selectedUser}
                  onChange={setSelectedUser}
                  placeholder='Select Persona'
                />
              </div>

              <div className='space-y-3'>
                <Label htmlFor='interviewDuration'>Interview Duration</Label>
                <Select
                  value={formData.interviewDuration}
                  onValueChange={(value) => updateFormData({ interviewDuration: value })}
                >
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Select Duration' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='10'>10 minutes</SelectItem>
                    <SelectItem value='20'>20 minutes</SelectItem>
                    <SelectItem value='30'>30 minutes</SelectItem>
                    <SelectItem value='40'>40 minutes</SelectItem>
                    <SelectItem value='50'>50 minutes</SelectItem>
                    <SelectItem value='60'>60 minutes</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <Button variant='secondary' disabled>
                  Create a new interview persona
                </Button>
                <Button onClick={handleAdd}>Add</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }
);

InterviewAgentStep.displayName = 'InterviewAgentStep';
