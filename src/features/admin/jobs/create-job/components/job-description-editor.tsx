'use client';

import { <PERSON><PERSON>, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui';
import { useJobStore } from '@/features/admin/jobs/create-job/hooks/useJobStore';
import { motion } from 'framer-motion';
import { useState } from 'react';
import ReactQuill from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';

interface JobDescriptionEditorProps {
  mode: 'ai' | 'manual';
  onBack: () => void;
}

export function JobDescriptionEditor({ mode, onBack }: JobDescriptionEditorProps) {
  const { nextStep, updateFormData } = useJobStore();
  const [description, setDescription] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ['align', 'color'],
      ['bold', 'italic', 'underline', 'strike'],
      [{ list: 'ordered' }, { list: 'bullet' }],
      ['link', 'code-block'],
    ],
  };

  const formats = [
    'header',
    'bold',
    'italic',
    'underline',
    'strike',
    'list',
    'link',
    'code-block',
    'align',
    'direction',
  ];

  const handleGenerateWithAI = async () => {
    setIsGenerating(true);
    // Simulate AI generation - replace with actual API call
    setTimeout(() => {
      const aiGeneratedContent = `
        <h2>About the Role</h2>
        <p>We are looking for a Product Manager (L-2) who will help shape the future of our digital products by owning key initiatives, driving user-centered innovation, and collaborating across cross-functional teams. This role is ideal for someone with 2-4 years of hands-on experience in product management who's ready to take the next step in their career.</p>
        
        <h2>Key Responsibilities</h2>
        <ul>
          <li>Translate business objectives and user needs into clear product strategies and roadmaps.</li>
          <li>Work closely with design, engineering, marketing, and customer success teams to deliver product features on time and with high quality.</li>
          <li>Gather and prioritize product and customer requirements through market research, user feedback, and competitive analysis.</li>
          <li>Define clear product goals and success metrics; track and report on performance.</li>
        </ul>
        
        <h2>Requirements</h2>
        <ul>
          <li>2-4 years of experience in a Product Management or related role.</li>
          <li>Strong understanding of Agile/Scrum methodologies.</li>
          <li>Experience working with cross-functional teams in a tech-driven environment.</li>
        </ul>
        
        <h2>What We Offer</h2>
        <ul>
          <li>Competitive salary</li>
          <li>Flexible working hours</li>
          <li>Friendly and collaborative team environment</li>
          <li>Annual performance bonus</li>
          <li>Learning & development support</li>
          <li>Location: In-office at our Dhaka HQ (with occasional remote flexibility)</li>
        </ul>
      `;
      setDescription(aiGeneratedContent);
      setIsGenerating(false);
    }, 2000);
  };

  const handleSaveAndNext = () => {
    updateFormData({ description });
    nextStep();
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className='mx-auto max-w-4xl'
    >
      <Card className='border-none bg-transparent shadow-none'>
        <CardHeader className='text-center'>
          <CardTitle className='text-xl font-bold'>Define the Role</CardTitle>
          <CardDescription className='text-md mx-auto max-w-xl text-neutral-500'>
            {mode === 'ai'
              ? 'Create your job post your way. Write a prompt to let AI generate it for you, or build it manually by filling out the form.'
              : 'Create your job description manually by writing the content yourself.'}
          </CardDescription>
        </CardHeader>

        <CardContent className='space-y-6 rounded-2xl border border-neutral-200 bg-white p-6'>
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <h3 className='text-lg font-semibold text-black'>Description</h3>
              {mode === 'ai' && (
                <Button
                  onClick={handleGenerateWithAI}
                  loading={isGenerating}
                  className='bg-blue-500 hover:bg-blue-600'
                  disabled={isGenerating}
                >
                  {isGenerating ? 'Generating...' : 'Rewrite with AI'}
                </Button>
              )}
            </div>

            <div className='min-h-[400px]'>
              <ReactQuill
                theme='snow'
                value={description}
                onChange={setDescription}
                modules={modules}
                formats={formats}
                placeholder='Write your job description here...'
                style={{ height: '350px' }}
              />
            </div>
          </div>

          <div className='flex justify-between pt-4'>
            <Button variant='outline' onClick={onBack}>
              Back
            </Button>
            <Button
              onClick={handleSaveAndNext}
              className='bg-blue-500 hover:bg-blue-600'
              disabled={!description.trim()}
            >
              Save & Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
