'use client';

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui';
import { useJobStore } from '@/features/admin/jobs/create-job/hooks/useJobStore';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import { forwardRef, useImperativeHandle } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const interviewSchema = z.object({
  interviewAgent: z.string().min(1, 'Interview agent is required'),
  interviewDuration: z.string().min(1, 'Interview duration is required'),
});

export const InterviewAgentStep = forwardRef<{ triggerValidation: () => Promise<boolean> }>(
  (_, ref) => {
    const { formData, updateFormData, nextStep, prevStep } = useJobStore();

    const form = useForm({
      resolver: zod<PERSON><PERSON><PERSON>ver(interviewSchema),
      defaultValues: {
        interviewAgent: formData.interviewAgent,
        interviewDuration: formData.interviewDuration,
      },
    });

    useImperativeHandle(ref, () => ({
      triggerValidation: async () => {
        return await form.trigger();
      },
    }));

    const handleNext = () => {
      nextStep();
    };

    const handleBack = () => {
      prevStep();
    };

    const handleAdd = () => {
      updateFormData({
        totalAgents: formData.totalAgents + 1,
        totalDuration: '30m', // Example duration
      });
    };

    return (
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
        className='mx-auto max-w-2xl'
      >
        <Card>
          <CardHeader className='text-center'>
            <CardTitle className='text-2xl'>Select Your AI Interviewers</CardTitle>
            <CardDescription>
              Choose AI agents best suited for this role. If you don&apos;t find a perfect match,
              you can create a new custom agent tailored to your needs.
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            {/* Stats */}
            <div className='grid grid-cols-2 gap-8 rounded-lg bg-neutral-200 p-6'>
              <div className='text-center'>
                <div className='text-muted-foreground text-sm'>Total Interview Agent</div>
                <div className='text-2xl font-bold'>{formData.totalAgents}</div>
              </div>
              <div className='text-center'>
                <div className='text-muted-foreground text-sm'>Total Duration</div>
                <div className='text-2xl font-bold'>{formData.totalDuration}</div>
              </div>
            </div>

            <div className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='interviewAgent'>Interview agent</Label>
                <Select
                  value={formData.interviewAgent}
                  onValueChange={(value) => updateFormData({ interviewAgent: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Placeholder' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='hr-specialist'>HR Specialist</SelectItem>
                    <SelectItem value='technical-lead'>Technical Lead</SelectItem>
                    <SelectItem value='senior-developer'>Senior Developer</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='interviewDuration'>Interview Duration</Label>
                <Select
                  value={formData.interviewDuration}
                  onValueChange={(value) => updateFormData({ interviewDuration: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Placeholder' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='15m'>15 minutes</SelectItem>
                    <SelectItem value='30m'>30 minutes</SelectItem>
                    <SelectItem value='45m'>45 minutes</SelectItem>
                    <SelectItem value='60m'>60 minutes</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='flex gap-3 pt-4'>
                <Button variant='outline'>Create a new interview agent</Button>
                <Button onClick={handleAdd} className='bg-blue-500 hover:bg-blue-600'>
                  Add
                </Button>
              </div>
            </div>

            <div className='flex justify-between pt-6'>
              <Button variant='outline' onClick={handleBack}>
                Back
              </Button>
              <Button onClick={handleNext} className='bg-blue-500 hover:bg-blue-600'>
                Next
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }
);

InterviewAgentStep.displayName = 'InterviewAgentStep';
