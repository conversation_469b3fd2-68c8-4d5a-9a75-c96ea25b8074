'use client';

import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface ProgressBarProps {
  currentStep: number;
  steps: {
    id: number;
    title: string;
  }[];
}

export function ProgressBar({ currentStep, steps }: ProgressBarProps) {
  return (
    <div className='flex items-center justify-center py-3'>
      {steps.map((step, index) => (
        <div key={step.id} className='flex items-center'>
          <div className='flex flex-col items-center'>
            <motion.span
              initial={false}
              animate={{
                color: currentStep >= step.id ? '#1f2937' : '#9ca3af',
                fontWeight: currentStep === step.id ? 600 : 400,
              }}
              transition={{ duration: 0.3 }}
              className={cn(
                'mb-2 p-2 text-sm',
                currentStep === step.id && '!rounded-lg !bg-gray-100'
              )}
            >
              {step.title}
            </motion.span>

            <div className='flex w-full items-center'>
              <motion.div
                initial={false}
                animate={{
                  backgroundColor:
                    currentStep === step.id
                      ? '#000000'
                      : currentStep > step.id
                        ? '#5C92FA'
                        : '#e5e7eb', // scale: currentStep === step.id ? 1.1 : 1,
                }}
                transition={{ duration: 0.3 }}
                className={cn(
                  'h-3 w-3 !translate-x-14 rounded-full',
                  currentStep >= step.id ? 'bg-blue-500' : 'bg-gray-300'
                )}
              />

              {index < steps.length - 1 && (
                <motion.div
                  initial={false}
                  animate={{
                    backgroundColor: currentStep > step.id ? '#5C92FA' : '#e5e7eb',
                  }}
                  transition={{ duration: 0.3 }}
                  className={cn('h-[1px] w-full !translate-x-14')}
                />
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
