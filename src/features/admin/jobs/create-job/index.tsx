'use client';

import { GradientCircle } from '@/components/shared/GradientCircle';
import { PageHeader } from '@/components/shared/PageHeader';
import { Button } from '@/components/ui';
import {
  InterviewAgentStep,
  JobDescriptionStep,
  PreviewStep,
  ProgressBar,
} from '@/features/admin/jobs/create-job/components';
import { useJobStore } from '@/features/admin/jobs/create-job/hooks/useJobStore';
import { AnimatePresence, motion } from 'framer-motion';
import { useRef } from 'react';

const steps = [
  { id: 1, title: 'Job description' },
  { id: 2, title: 'Interview agent' },
  { id: 3, title: 'Preview & Publish' },
];

const breadcrumbsItem = [
  { label: 'Home', href: '/admin/dashboard' },
  { label: 'Job list', href: '/admin/jobs' },
  { label: 'Create a job post' },
];

export default function CreateJobPage() {
  const { currentStep, nextStep, prevStep } = useJobStore();
  const stepFormRef = useRef<{ triggerValidation: () => Promise<boolean> }>(null);

  const handleNextStep = async () => {
    // Trigger validation for current step
    const isValid = await stepFormRef.current?.triggerValidation();
    if (isValid) {
      nextStep();
    }
  };

  const handleBack = () => {
    prevStep();
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        // return <JobDescriptionStep ref={stepFormRef} />;
        return <InterviewAgentStep ref={stepFormRef} />;
        return <PreviewStep />;
      case 2:
        return <InterviewAgentStep ref={stepFormRef} />;
      case 3:
        return <PreviewStep />;
      default:
        return <JobDescriptionStep ref={stepFormRef} />;
    }
  };

  return (
    <div className='dark:bg-background flex h-full flex-col bg-neutral-100'>
      <PageHeader title='Create Job' showNavigation={true} breadcrumbs={breadcrumbsItem} />

      <div className='flex flex-1 overflow-y-auto px-6'>
        <div className='bg-card mx-auto mb-4 min-h-max w-full rounded-2xl border'>
          {/* Header */}
          <div className='flex items-center justify-between gap-4 rounded-t-2xl border-b px-6 py-3 shadow-sm'>
            <div className='text-2xl font-bold'>Create New Job</div>
            <ProgressBar currentStep={currentStep} steps={steps} />
            <div className='flex'>
              {currentStep !== 1 && (
                <Button className='mr-2' variant='outline' onClick={handleBack}>
                  Back
                </Button>
              )}
              <Button className='' variant='default' onClick={handleNextStep}>
                Next
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className='relative isolate overflow-hidden p-6'>
            <div className='pointer-events-none absolute inset-0 -z-10'>
              <GradientCircle className='-top-120 left-1/2 -translate-x-1/2 !opacity-10' />
            </div>
            <AnimatePresence mode='wait'>
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className='z-30'
              >
                {renderStep()}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
}
