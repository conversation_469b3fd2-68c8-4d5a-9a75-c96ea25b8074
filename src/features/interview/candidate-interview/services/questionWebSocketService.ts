import { InterviewItem } from '@/services/interview';
import SessionStorageManager, { SESSION_STORAGE_KEYS } from '@/utils/sessionStorage';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

export interface WebSocketMessage {
  type: 'audio' | 'text' | 'info';
  audio_data?: string; // base64 blob
  text?: string; // question string (legacy)
  content?: string; // question string (new format)
  channel_id?: string;
  chat_id?: string;
  message?: string; // for info messages
}

export interface QuestionWebSocketRequest {
  channel_id: string;
  role: string;
  cv_s3_key?: string;
  difficulty: string;
  prompt_text: string;
  qa_array: {
    question: string;
    answer: string;
  }[];
  remaining_time?: number;
}

class QuestionWebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;
  private messageHandlers: ((message: WebSocketMessage) => void)[] = [];

  connect(channelId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const wsUrl = `${import.meta.env.VITE_PREVIA_QUESTIONS_WS_URL}/${channelId}`;

      this.ws = new WebSocket(wsUrl);

      

      this.ws.onopen = () => {
        console.log('WebSocket connected for question generation');
        this.reconnectAttempts = 0;
        resolve();
      };

      this.ws.onmessage = (event) => {
        try {
          console.log('WebSocket event:', event);
          const message = JSON.parse(event.data);
          console.log('WebSocket message:', message);
          this.messageHandlers.forEach((handler) => handler(message));
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log('WebSocket closed', event.code, event.reason);
        // Only attempt reconnect if not a normal closure (code !== 1000)
        if (event.code !== 1000) {
          this.attemptReconnect(channelId);
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        reject(error);
      };
    });
  }

  private attemptReconnect(channelId: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(
          `Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`
        );
        this.connect(channelId);
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  sendQuestionRequest(request: QuestionWebSocketRequest) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(request));
    } else {
      console.error('WebSocket is not connected');
    }
  }

  addMessageHandler(handler: (message: WebSocketMessage) => void) {
    this.messageHandlers.push(handler);
  }

  removeMessageHandler(handler: (message: WebSocketMessage) => void) {
    this.messageHandlers = this.messageHandlers.filter((h) => h !== handler);
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.messageHandlers = [];
  }
}

export const questionWebSocketService = new QuestionWebSocketService();

// React Query hook for WebSocket connection
export function useQuestionWebSocket() {
  const queryClient = useQueryClient();
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);

  const interviewItems = SessionStorageManager.getItem<InterviewItem[]>(
    SESSION_STORAGE_KEYS.INTERVIEW_ITEMS
  );
  const channelId = interviewItems?.[0]?.id || 'uuid-' + Math.random().toString(36).substr(2, 9);

  useEffect(() => {
    console.log('channelId', channelId);
    if (!channelId) return;

    const messageHandler = (message: WebSocketMessage) => {
      setLastMessage(message);
      queryClient.invalidateQueries({ queryKey: ['question-websocket', channelId] });
    };

    questionWebSocketService.addMessageHandler(messageHandler);

    questionWebSocketService
      .connect(channelId)
      .then(() => setIsConnected(true))
      .catch(() => setIsConnected(false));

    return () => {
      questionWebSocketService.removeMessageHandler(messageHandler);
      // questionWebSocketService.disconnect();
      setIsConnected(false);
    };
  }, [channelId]);

  return {
    isConnected,
    lastMessage,
    channelId,
    sendQuestionRequest: (request: Omit<QuestionWebSocketRequest, 'channel_id'>) => {
      if (channelId) {
        questionWebSocketService.sendQuestionRequest({
          ...request,
          channel_id: channelId,
        });
      }
    },
  };
}
