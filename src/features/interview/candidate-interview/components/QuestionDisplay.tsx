import { timerService } from '../services/timerService';
import { currentQuestion } from '../signals/interviewSignals';
import {
  currentPhase,
  currentQuestionStatus,
  InterviewPhase,
  QuestionStatus,
} from '../signals/timerSignals';
import PreviaAvatar from '@/assets/icons/previa-avatar';
import { SoundWave } from '@/assets/icons/sound-wave';
import { TextShimmer } from '@/components/shared/text-shimmering';
import { useGenerateQuestionSyncMutation } from '@/hooks/api/use-interview';
import { InterviewItem } from '@/services/interview';
import { sleep } from '@/utils/helper';
import { InterviewSessionStorage } from '@/utils/sessionStorage';
import { useSignals } from '@preact/signals-react/runtime';
import { useEffect, useRef, useState } from 'react';

export function QuestionDisplay() {
  useSignals();
  const [isLoadingQuestion, setIsLoadingQuestion] = useState(false);
  const [hasInitialQuestionRequested, setHasInitialQuestionRequested] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);
  const audioPlayingRef = useRef(false);
  const requestInProgress = useRef(false);

  const generateQuestionSyncMutation = useGenerateQuestionSyncMutation();

  // Request initial question when component mounts
  useEffect(() => {
    if (!hasInitialQuestionRequested) {
      requestQuestion();
      setHasInitialQuestionRequested(true);
    }
  }, [hasInitialQuestionRequested]);

  const requestQuestion = async () => {
    if (isLoadingQuestion || requestInProgress.current) return;

    requestInProgress.current = true;
    setIsLoadingQuestion(true);

    const interviewData = InterviewSessionStorage.getInterviewItems() as InterviewItem[];

    try {
      const response = await generateQuestionSyncMutation.mutateAsync({
        channel_id: interviewData?.[0]?.id,
      });

      // Store channel_id and chat_id in session storage
      InterviewSessionStorage.setChannelId(response.channel_id);
      InterviewSessionStorage.setChatId(response.chat_id);

      // Handle the response with question text and audio
      const questionId = `q_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      currentQuestion.value = {
        text: response.content,
        expectedDuration: response.audio_length_in_milliseconds / 1000 || 30,
        isLoading: false,
        questionId: questionId,
        audioData: response.audio_data,
      };

      currentPhase.value = InterviewPhase.READING_QUESTION;

      // Play audio if available
      if (response.audio_data) {
        playAudioFromBase64(response.audio_data);
      } else {
        timerService.startThinkingTimer();
      }
    } catch (error) {
      console.error('Failed to generate question:', error);
    } finally {
      setIsLoadingQuestion(false);
      requestInProgress.current = false;
    }
  };

  const playAudioFromBase64 = async (base64Audio: string) => {
    try {
      const audioBlob = base64ToBlob(base64Audio, 'audio/wav');
      const audioUrl = URL.createObjectURL(audioBlob);

      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }

      if (audioRef.current) {
        audioRef.current.src = audioUrl;
        await new Promise((resolve) => setTimeout(resolve, 100));
        await audioRef.current.play();

        audioRef.current.onended = () => {
          URL.revokeObjectURL(audioUrl);

          if (currentQuestionStatus.value === QuestionStatus.IS_LAST_QUESTION) {
            handleInterviewEnd();
            return;
          }
          timerService.startThinkingTimer();
        };
      }
    } catch (error) {
      console.error('Failed to play TTS audio:', error);
      timerService.startThinkingTimer();
    }
  };

  const base64ToBlob = (base64: string, mimeType: string): Blob => {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  };

  // const showCompletionMessage = () => {
  //   const completionMessage = `Thank you for participating in this interview. Your responses have been recorded and our team will review them shortly.`;

  //   currentQuestion.value = {
  //     ...currentQuestion.value,
  //     text: completionMessage,
  //     isLoading: false,
  //   };
  // };

  const getDisplayContent = () => {
    if (
      (isLoadingQuestion || currentPhase.value === InterviewPhase.LOADING_QUESTION) &&
      (!currentQuestion.value.text || currentQuestion.value.isLoading)
    ) {
      return {
        speaker: 'Previa',
        message: 'Preparing your question...',
        isQuestionLoading: true,
      };
    }

    return {
      speaker: 'Previa',
      message: currentQuestion.value.text,
      isQuestionLoading: false,
    };
  };

  const content = getDisplayContent();

  const handleInterviewEnd = async () => {
    await sleep(2000);
    currentPhase.value = InterviewPhase.COMPLETED;
    timerService.completeInterview('all_questions');
  };

  // Watch for phase changes to handle audio playback
  useEffect(() => {
    console.log('currentPhase.', currentQuestionStatus.value);
    if (
      currentPhase.value === InterviewPhase.READING_QUESTION &&
      currentQuestion.value.audioData &&
      !audioPlayingRef.current
    ) {
      audioPlayingRef.current = true;
      playAudioFromBase64(currentQuestion.value.audioData).finally(() => {
        audioPlayingRef.current = false;
      });
    } else if (
      currentPhase.value === InterviewPhase.READING_QUESTION &&
      !currentQuestion.value.audioData
    ) {
      if (currentQuestionStatus.value === QuestionStatus.IS_LAST_QUESTION) {
        handleInterviewEnd();
        return;
      }
      timerService.startThinkingTimer();
    }
  }, [currentPhase.value, currentQuestion.value.audioData]);

  return (
    <div className='flex items-start gap-6'>
      <audio ref={audioRef} style={{ display: 'none' }} />
      <div className='flex-shrink-0'>
        <div className='gradient-border flex size-40 flex-col items-center justify-center gap-5.5 rounded-2xl border bg-gray-50'>
          <PreviaAvatar className='size-15' />
          <div className='flex w-full items-center justify-around'>
            <span className='text-gray-dark text-lg font-medium'>Previa</span>
            <SoundWave className='h-5.5 w-auto' />
          </div>
        </div>
      </div>
      <div className='flex-1'>
        {/* <div className='mb-4'>
          <span className='text-lg leading-relaxed font-medium text-gray-900'>
            {content.speaker}
          </span>
        </div> */}
        <div className='rounded-lg'>
          {content.isQuestionLoading ? (
            <TextShimmer className='text-2xl leading-[40px]'>
              Preparing your question...
            </TextShimmer>
          ) : (
            <p className='text-2xl leading-[40px]'>{content.message}</p>
          )}
        </div>
      </div>
    </div>
  );
}
