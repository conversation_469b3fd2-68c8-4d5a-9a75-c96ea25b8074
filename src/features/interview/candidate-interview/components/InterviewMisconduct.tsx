import { useSignals } from '@preact/signals-react/runtime';
import { AlertTriangle, Camera, Mic } from 'lucide-react';
import { misconductState } from '../signals/interviewSignals';
import { useNavigate } from '@tanstack/react-router';

export function InterviewMisconduct() {
  useSignals();
  const navigate = useNavigate();

  const getMisconductMessage = () => {
    switch (misconductState.value.reason) {
      case 'camera_blocked':
        return {
          title: 'Camera Access Blocked',
          message: 'Your camera access has been blocked or disabled during the interview.',
          icon: Camera
        };
      case 'microphone_blocked':
        return {
          title: 'Microphone Access Blocked', 
          message: 'Your microphone access has been blocked or disabled during the interview.',
          icon: Mic
        };
      case 'both_blocked':
        return {
          title: 'Media Access Blocked',
          message: 'Camera or Microphone access have been blocked during the interview.',
          icon: AlertTriangle
        };
      default:
        return {
          title: 'Interview Misconduct Detected',
          message: 'The interview has been terminated due to technical violations.',
          icon: AlertTriangle
        };
    }
  };

  const { title, message, icon: Icon } = getMisconductMessage();

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="max-w-md p-8 text-center bg-white rounded-lg shadow-lg">
        <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
          <Icon className="h-8 w-8 text-red-600" />
        </div>
        
        <h2 className="mb-4 text-2xl font-bold text-gray-900">{title}</h2>
        
        <p className="mb-6 text-gray-600">{message}</p>
        
        <div className="rounded-lg bg-red-50 p-4 mb-6">
          <p className="text-sm text-red-800">
            <strong>Interview Terminated:</strong> This interview session has been ended due to 
            media access violations. Please contact support if you believe this was an error.
          </p>
        </div>

        <button
          onClick={() => navigate({ to: '/' })}
          className="w-full rounded-md bg-gray-600 py-2 px-4 text-white hover:bg-gray-700"
        >
          Return to Job List
        </button>
      </div>
    </div>
  );
}