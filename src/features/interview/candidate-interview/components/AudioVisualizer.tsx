import { motion } from "framer-motion";
import { useEffect, useRef, useState } from 'react';
import { mediaState } from '../signals/interviewSignals';
import { useSignals } from '@preact/signals-react/runtime';

interface AudioVisualizerProps {
  isActive: boolean;
  className?: string;
}

export function AudioVisualizer({ isActive, className = "" }: AudioVisualizerProps) {
  useSignals();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const visualize = () => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!ctx || !analyserRef.current || !canvas) return;

    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      if (!isActive) return;
      
      animationFrameRef.current = requestAnimationFrame(draw);
      
      analyserRef.current!.getByteTimeDomainData(dataArray);

      ctx.fillStyle = "rgba(239, 246, 255, 0.2)";
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Draw center line first (baseline)
      ctx.strokeStyle = "#e5e7eb";
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(0, canvas.height / 2);
      ctx.lineTo(canvas.width, canvas.height / 2);
      ctx.stroke();

      // Draw waveform
      ctx.lineWidth = 2;
      ctx.strokeStyle = "#2563eb";
      ctx.beginPath();

      const sliceWidth = canvas.width / bufferLength;
      let x = 0;

      for (let i = 0; i < bufferLength; i++) {
        const v = dataArray[i] / 128.0;
        const y = (v * canvas.height) / 2;

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }

        x += sliceWidth;
      }

      ctx.lineTo(canvas.width, canvas.height / 2);
      ctx.stroke();
    };

    draw();
  };

  const startVisualization = async () => {
    if (!mediaState.value.stream || isInitialized) return;

    try {
      const audioTracks = mediaState.value.stream.getAudioTracks();
      if (audioTracks.length === 0) return;

      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 2048;
            analyserRef.current.smoothingTimeConstant = 0.8;

      
      const source = audioContextRef.current.createMediaStreamSource(mediaState.value.stream);
      source.connect(analyserRef.current);
      
      setIsInitialized(true);
      visualize();
    } catch (error) {
      console.error('Error starting audio visualization:', error);
    }
  };

  const stopVisualization = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
    
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (ctx && canvas) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
    
    setIsInitialized(false);
  };

useEffect(() => {
    if (isActive && mediaState.value.isAudioEnabled) {
      startVisualization();
    } else {
      stopVisualization();
      // Draw baseline when inactive but should show canvas
      if (isActive && canvasRef.current) {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext("2d");
        if (ctx) {
          ctx.fillStyle = "rgba(239, 246, 255, 0.2)";
          ctx.fillRect(20, 20, canvas.width, canvas.height);
          ctx.strokeStyle = "#2563eb";
          ctx.lineWidth = 10;
          ctx.beginPath();
          ctx.moveTo(0, canvas.height / 2);
          ctx.lineTo(canvas.width, canvas.height / 2);
          ctx.stroke();
        }
      }
    }

    return () => {
      stopVisualization();
    };
  }, [isActive, mediaState.value.isAudioEnabled]);



  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ 
        opacity: isActive && mediaState.value.isAudioEnabled ? 1 : 0, 
        height: isActive && mediaState.value.isAudioEnabled ? "auto" : 0 
      }}
      transition={{ duration: 0.3 }}
      className={`overflow-hidden ${className}`}
    >
      <canvas
        ref={canvasRef}
        width={600}
        height={100}
        className="w-full rounded-lg border border-blue-200 bg-blue-50"
      />
    </motion.div>
  );
}