import { interviewSession } from '../signals/interviewSignals';
import EvaluationContent from './EvaluationContent';
import { useRunEvaluationQuery } from '@/hooks/api/use-interview';
import { useSignals } from '@preact/signals-react/runtime';
import { CheckCircle } from 'lucide-react';

export function InterviewCompletion() {
  useSignals();

  //   useEffect(() => {
  //   window.history.replaceState(null, '', '/interview-complete');
  //   // Add a new history entry for root so back button works correctly
  //   window.history.pushState(null, '', '/');
  //   window.history.back();
  // }, []);

  const { data: evaluationData, isLoading: isEvaluationLoading } = useRunEvaluationQuery(
    interviewSession.value.interviewId,
    { enabled: !!interviewSession.value.interviewId }
  );

  // const getCompletionMessage = () => {
  //   switch (interviewProgress.value.completionReason) {
  //     case 'time_up':
  //       return 'Interview completed - Time limit reached';
  //     case 'all_questions':
  //       return 'Interview completed - All questions answered';
  //     case 'manual':
  //       return 'Interview completed - Manually ended';
  //     default:
  //       return 'Interview completed';
  //   }
  // };

  return (
    <div className='flex min-h-screen flex-col items-center justify-center bg-gray-50 p-6'>
      <div className='w-full max-w-4xl rounded-lg bg-white p-8 shadow-lg'>
        <div className='mb-6 text-center'>
          <CheckCircle className='mx-auto mb-4 h-16 w-16 text-green-500' />
          <h1 className='mb-2 text-3xl font-bold text-gray-900'>Interview Complete!</h1>
          {/* <p className='text-lg text-gray-600'>
            {getCompletionMessage()}
          </p> */}
        </div>

        <div className='mb-6'>
          <h2 className='mb-4 text-xl font-semibold text-gray-900'>Interview Evaluation</h2>
          <EvaluationContent
            evaluation={evaluationData?.evaluation_data || null}
            isCandidateDataLoading={isEvaluationLoading}
          />
        </div>

        <div className='space-y-3 text-center'>
          <p className='text-sm text-gray-500'>
            Need help?{' '}
            <a
              href='mailto:<EMAIL>'
              className='text-blue-600 underline transition-colors hover:text-blue-800'
              title='<EMAIL>'
            >
              Contact support
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
