import { timerService } from '../services/timerService';
import { answerState, currentQuestion, qaHistory } from '../signals/interviewSignals';
import {
  canRequestNewQuestion,
  currentPhase,
  currentQuestionStatus,
  defaultTimerConfig,
  editTimeRemaining,
  InterviewPhase,
  QuestionStatus,
} from '../signals/timerSignals';
import PreviaAvatar from '@/assets/icons/previa-avatar';
import { CircularTimer } from '@/components/shared/circularr-timer';
import { Button } from '@/components/ui';
import {
  useGenerateQuestionSyncMutation,
  useUpdateAnswerMutation,
} from '@/hooks/api/use-interview';
import { InterviewItem } from '@/services/interview';
import { InterviewSessionStorage } from '@/utils/sessionStorage';
import { useSignals } from '@preact/signals-react/runtime';
import { ClockFading } from 'lucide-react';
import { useEffect, useRef } from 'react';

export function TranscriptionEditor() {
  useSignals();
  const generateQuestionSyncMutation = useGenerateQuestionSyncMutation();
  const updateAnswerMutation = useUpdateAnswerMutation();
  const audioRef = useRef<HTMLAudioElement>(null);

  const handleSubmit = async () => {
    try {
      const interviewData = InterviewSessionStorage.getInterviewItems() as InterviewItem[];
      const channelId = InterviewSessionStorage.getChannelId();
      const chatId = InterviewSessionStorage.getChatId();

      // First, update the answer
      await updateAnswerMutation.mutateAsync({
        channel_id: channelId,
        chat_id: chatId,
        answer: answerState.value.editedAnswer,
      });

      console.log('Answer updated, generating next question');
      // Add to Q&A history before clearing current question
      qaHistory.value = [
        ...qaHistory.value,
        {
          question: currentQuestion.value.text,
          answer: answerState.value.editedAnswer,
        },
      ];

      // Clear current question and set to loading phase
      currentQuestion.value = {
        text: '',
        expectedDuration: 30,
        isLoading: true,
        questionId: '',
        audioData: undefined,
      };

      timerService.resetForNewQuestion();

      currentPhase.value = InterviewPhase.LOADING_QUESTION;

      const isLastQuestion = !canRequestNewQuestion.value;

      if (isLastQuestion) {
        currentQuestionStatus.value = QuestionStatus.IS_LAST_QUESTION;
      }

      // Then generate the next question
      const syncResponse = await generateQuestionSyncMutation.mutateAsync({
        channel_id: interviewData?.[0]?.id,
        is_interview_ending_question: isLastQuestion,
      });

      // Update current question with the new content
      const questionId = `q_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      currentQuestion.value = {
        text: syncResponse.content,
        expectedDuration: syncResponse.audio_length_in_milliseconds / 1000 || 30,
        isLoading: false,
        questionId: questionId,
        audioData: syncResponse.audio_data,
      };

      // Store updated session data
      InterviewSessionStorage.setChannelId(syncResponse.channel_id);
      InterviewSessionStorage.setChatId(syncResponse.chat_id);

      // Set phase to reading question
      currentPhase.value = InterviewPhase.READING_QUESTION;
    } catch (error) {
      console.error('Failed to submit answer:', error);
    }

    // Reset answer state
    answerState.value = {
      currentAnswer: '',
      transcribedText: '',
      editedAnswer: '',
      isRecording: false,
      isTranscribing: false,
      recordingBlob: null,
    };
  };

  //   useEffect(() => {
  //   const handleAutoSubmit = () => {
  //     handleSubmit();
  //   };
  //   console.log('timer auto reg');
  //   timerService.registerAutoSubmitCallback('edit', handleAutoSubmit);

  //   return () => {
  //     timerService.unregisterAutoSubmitCallback('edit');
  //   };
  // }, []);

  useEffect(() => {
    if (currentPhase.value == InterviewPhase.EDITING) {
      console.log('test;');
      timerService.startEditTimer(() => {
        console.log('edit timer ended, submitting answer');
        handleSubmit();
      });
    }
  }, [currentPhase.value]);

  if (currentPhase.value === InterviewPhase.TRANSCRIBING) {
    return (
      <div className='flex h-full items-center justify-center bg-gray-50 p-8'>
        <audio ref={audioRef} style={{ display: 'none' }} />
        <div className='flex max-w-md flex-col items-center text-center'>
          <PreviaAvatar className='mb-4 size-10' />

          <h3 className='mb-1 text-xl font-semibold text-gray-900'>
            Just a moment... we're analyzing your response
          </h3>

          <p className='text-sm text-gray-600'>
            After transcription, you'll have 30 sec to review your answer.
          </p>
        </div>
      </div>
    );
  }

  if (currentPhase.value === InterviewPhase.EDITING) {
    return (
      <div className='flex h-full flex-col space-y-4'>
        <audio ref={audioRef} style={{ display: 'none' }} />
        {/* Header with timer */}
        <div className='flex items-center justify-between'>
          <h3 className='text-gray-dark text-2xl font-semibold'>Your answer</h3>
          <div className='flex items-center gap-2 text-sm text-orange-600'>
            <ClockFading className='size-4' />
            <p className='text-gray-light'>Time to Edit: </p>
            <CircularTimer
              totalTime={defaultTimerConfig.EDITING_TIMER}
              currentTime={editTimeRemaining.value}
            />
          </div>
        </div>

        {/* Answer textarea */}
        <textarea
          className='border-gray-light focus-visible:border-gray-light h-full w-full flex-1 resize-none rounded-md border px-5 py-4 text-lg text-black focus-visible:outline-none'
          value={answerState.value.editedAnswer}
          onChange={(e) =>
            (answerState.value = {
              ...answerState.value,
              editedAnswer: e.target.value,
            })
          }
          placeholder='Type or edit your answer here...'
          onPaste={(e) => e.preventDefault()}
          onCopy={(e) => e.preventDefault()}
          onCut={(e) => e.preventDefault()}
          onDrop={(e) => e.preventDefault()}
          onDragOver={(e) => e.preventDefault()}
        />

        {/* Submit button */}
        <Button
          onClick={handleSubmit}
          disabled={generateQuestionSyncMutation.isPending || updateAnswerMutation.isPending}
          className='flex w-full items-center justify-center gap-2 rounded-md bg-[linear-gradient(141.88deg,#5C92FA_35.25%,#A75FFD_107.8%)] disabled:opacity-50'
        >
          {updateAnswerMutation.isPending ? (
            <>
              <div className='h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent'></div>
              Submitting...
            </>
          ) : (
            <>
              <span>✓</span>
              Submit my answer
            </>
          )}
        </Button>

        {updateAnswerMutation.isError && (
          <p className='text-center text-sm text-red-500'>
            Failed to submit answer, but continuing interview
          </p>
        )}
      </div>
    );
  }

  return null;
}
