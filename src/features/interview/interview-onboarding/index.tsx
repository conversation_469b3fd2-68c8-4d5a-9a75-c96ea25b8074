import { AIIcon } from '@/assets';
import { useCandidateStore } from '@/stores/candidate-store';
import SessionStorageManager, { SESSION_STORAGE_KEYS } from '@/utils/sessionStorage';
import { useNavigate } from '@tanstack/react-router';
import { useEffect } from 'react';

const InterviewOnboarding = () => {
  const navigate = useNavigate();
  const { candidateId } = useCandidateStore();

  useEffect(() => {
    if (candidateId) {
      SessionStorageManager.setItem(SESSION_STORAGE_KEYS.CANDIDATE_DATA, {
        candidate_id: candidateId,
      });
    }
  }, [candidateId]);

  return (
    <>
      <div className='flex p-3 md:p-6'>
        <div className='flex h-[calc(100vh-72px-12px-12px)] w-full flex-col rounded-2xl border md:h-[calc(100vh-72px-24px-24px)] md:flex-row'>
          <div
            className='hidden flex-7/12 rounded-l-2xl md:flex md:w-2/3'
            style={{
              backgroundImage: 'url(/images/previa-interview-onboarding.png)',
              backgroundSize: '100% 105%',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              boxShadow: 'rgb(255 255 255) 0 -161px 100px 0 inset',
            }}
          >
            <div className='flex h-full flex-col justify-end gap-6 p-14'>
              <AIIcon />
              <div className='flex flex-col gap-4'>
                <h1 className='text-3xl font-semibold text-black'>
                  Welcome,
                  <br />
                  You're Invited to a Demo Interview
                </h1>
              </div>
            </div>
          </div>
          <div className='w-full flex-5/12 md:w-1/3'>
            <div className='flex min-h-[calc(100vh-72px-12px-12px)] items-center justify-center md:min-h-[calc(100vh-72px-24px-24px)]'>
              <div className='w-full space-y-4 px-4 md:space-y-6 md:px-10'>
                <div className='mb-6 md:hidden'>
                  <div className='flex flex-col gap-4'>
                    <h1 className='text-3xl font-semibold text-black'>
                      Welcome,
                      <br />
                      You're Invited to a Demo Interview
                    </h1>
                  </div>
                </div>
                <h1 className='text-2xl font-semibold text-gray-900'>Demo Interview Experience</h1>

                <p className='text-gray-700'>
                  Welcome to the Demo Interview. In this demo, you'll experience the interview
                  journey powered by
                  <strong> Previa</strong>, showcasing how AI transforms candidate assessment.
                </p>

                <div className='rounded-md border border-orange-300 bg-orange-100 p-4 text-sm text-orange-800'>
                  <p>
                    <strong>Before You Begin,</strong> To start this interview, you'll need to
                    enable your <strong>webcam and microphone with a laptop device</strong>. You'll
                    be speaking your answers aloud. we'll transcribe your responses for review.
                  </p>
                </div>

                <button
                  className='w-full rounded-md bg-blue-600 py-2.5 text-sm font-medium text-white hover:bg-blue-700'
                  onClick={() => {
                    navigate({ to: '/interview-onboarding-system-setup' });
                  }}
                >
                  Start Interview
                </button>

                <p className='text-center text-xs text-gray-600 md:text-sm'>
                  Need any help? Please{' '}
                  <a
                    href='mailto:<EMAIL>'
                    className='text-blue-600 underline transition-colors hover:text-blue-800'
                    title='<EMAIL>'
                  >
                    Contact Us
                  </a>{' '}
                  for assistance
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default InterviewOnboarding;
