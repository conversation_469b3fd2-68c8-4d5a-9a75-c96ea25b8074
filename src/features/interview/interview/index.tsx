import { InterviewSession } from '../candidate-interview/components/InterviewSession';
import { timerService } from '../candidate-interview/services/timerService';
import { interviewSession } from '../candidate-interview/signals/interviewSignals';
import { useCompleteInterviewMutation } from '@/hooks/api/use-interview';
import { InterviewItem } from '@/services/interview';
import { useCandidateStore } from '@/stores/candidate-store';
import SessionStorageManager, { SESSION_STORAGE_KEYS } from '@/utils/sessionStorage';
import { useSignals } from '@preact/signals-react/runtime';
import { useEffect, useState } from 'react';

interface CandidateData {
  candidate_id: string;
  job_id: string;
}

const Interview = () => {
  useSignals();

  const { candidateId } = useCandidateStore();
  const [isInitializing, setIsInitializing] = useState(true);
  const completeInterviewMutation = useCompleteInterviewMutation();
  const candidateData = SessionStorageManager.getItem<CandidateData>(
    SESSION_STORAGE_KEYS.CANDIDATE_DATA
  );
  const interviewItems = SessionStorageManager.getItem<InterviewItem[]>(
    SESSION_STORAGE_KEYS.INTERVIEW_ITEMS
  );

  useEffect(() => {
    const initializeInterview = async () => {
      setIsInitializing(true);

      try {
        if (!candidateData || !interviewItems || !candidateId) {
          console.warn('Missing candidate data or interview items');
          return;
        }

        const totalDuration =
          interviewItems.reduce(
            (total: number, item: InterviewItem) => total + item.time_duration * 60,
            0
          ) || 1800;

        interviewSession.value = {
          interviewId: interviewItems?.[0]?.id,
          totalDuration: totalDuration,
        };

        // Register completion callback
        timerService.registerCompleteInterviewCallback(async (interviewId: string) => {
          await completeInterviewMutation.mutateAsync(interviewId);
        });

        timerService.startOverallTimer(totalDuration);
      } catch (error) {
        console.error('Failed to initialize interview:', error);
      } finally {
        setIsInitializing(false);
      }
    };

    initializeInterview();

    return () => {
      timerService.cleanup();
    };
  }, [completeInterviewMutation]);

  if (isInitializing) {
    return (
      <div className='flex min-h-screen items-center justify-center'>
        <div className='text-center'>
          <div className='mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600'></div>
          <p className='text-gray-600'>Initializing your interview session...</p>
          <p className='mt-2 text-sm text-gray-500'>Loading interview data</p>
        </div>
      </div>
    );
  }

  return <InterviewSession />;
};

export default Interview;
