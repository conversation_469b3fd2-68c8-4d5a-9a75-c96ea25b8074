declare global {
  interface JobApplyPayload {
    full_name: string;
    email: string;
    phone_number: string;
    address: string;
    years_of_experience: number;
    cv_link: string;
  }

  interface JobApplyRequest {
    jobId: string;
    payload: JobApplyPayload;
  }

  // Candidate Profile Create

  type CandidateProfileCreatePayload = Pick<
    JobApplyPayload,
    'full_name' | 'email' | 'phone_number'
  >;

  interface CandidateProfileCreateResponse {
    id: string;
    full_name: string;
    email: string;
  }

  // Email Verification
  interface EmailVerificationRequest {
    candidate_id: string;
    otp: string;
  }

  interface EmailVerificationResponse {
    candidateId: string;
    email_verification: boolean;
    verified_at: string;
  }

  // Candidate Profile Complete
  type CandidateProfileCompletePayload = Pick<
    JobApplyPayload,
    'address' | 'years_of_experience' | 'cv_link'
  >;
}

export {};
