declare global {
  interface InterviewAgent {
    id: string;
    name: string;
    role: string;
    difficulty: 'Easy' | 'Medium' | 'Hard';
    duration: number; // in minutes
    jobsCount: number;
    persona: string;
    avatar?: string;
    createdAt: Date;
    lastUsed?: Date;
  }

  interface AgentCardProps {
    agent: InterviewAgent;
    onEdit?: (agent: InterviewAgent) => void;
    onDelete?: (agentId: string) => void;
    onDuplicate?: (agent: InterviewAgent) => void;
    onViewDetails?: (agent: InterviewAgent) => void;
  }
}
export {};
