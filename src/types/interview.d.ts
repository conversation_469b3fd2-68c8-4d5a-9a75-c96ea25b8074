interface CandidateList {
  id: number;
  email: string;
  position_applied_for: string;
  full_name: string;
  name?: string;
  phone_number: string;
  address: string;
  skills: string[];
  job_id: number;
  years_of_experience: string;
  status: CandidateInterviewStatus;
  avg_score: number;
  avatar?: string;
  cv_link?: string;
  interview_date?: string;
  eligibility_info?: ElegiblityData;
}

interface DashboardJob {
  id: number;
  title: string;
  location: string;
  min_exp: number;
  max_exp: number;
  is_active: boolean;
  application_end_date: string;
  candidate_count: {
    total: number;
    attended: number;
    not_attended: number;
  };
}

interface SpeechRecognitionEvent extends Event {
  readonly resultIndex: number;
  readonly results: SpeechRecognitionResultList;
}

interface SpeechRecognitionErrorEvent extends Event {
  readonly error: string;
  readonly message: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  abort(): void;

  onaudioend: ((this: SpeechRecognition, ev: Event) => void) | null;
  onaudiostart: ((this: SpeechRecognition, ev: Event) => void) | null;
  onend: ((this: SpeechRecognition, ev: Event) => void) | null;
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => void) | null;
  onnomatch: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => void) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => void) | null;
  onsoundend: ((this: SpeechRecognition, ev: Event) => void) | null;
  onsoundstart: ((this: SpeechRecognition, ev: Event) => void) | null;
  onspeechend: ((this: SpeechRecognition, ev: Event) => void) | null;
  onspeechstart: ((this: SpeechRecognition, ev: Event) => void) | null;
  onstart: ((this: SpeechRecognition, ev: Event) => void) | null;
}

interface Window {
  webkitSpeechRecognition: {
    new (): SpeechRecognition;
  };
}