import { LinkProps } from '@tanstack/react-router';

interface SidebarIconProps extends React.SVGProps<SVGSVGElement> {
  variant?: 'active' | 'default';
}

interface SidebarHeaderProps {
  isCollapsed: boolean;
  onToggle: () => void;
}
interface ProfileMenuProps {
  userAvatar?: string;
  userName?: string;
  userEmail?: string;
}

interface BaseNavItem {
  title: string;
  badge?: string;
  icon?: React.ElementType;
}

type NavLink = BaseNavItem & {
  url: LinkProps['to'];
  items?: never;
};

type NavCollapsible = BaseNavItem & {
  items: (BaseNavItem & { url: LinkProps['to'] })[];
  url?: never;
};

type NavItem = NavCollapsible | NavLink;

interface NavGroup {
  title: string;
  items: NavItem[];
}

interface SidebarData {
  user?: {
    name: string;
    email: string;
    avatar: string;
  };
  navGroups: NavGroup[];
}
