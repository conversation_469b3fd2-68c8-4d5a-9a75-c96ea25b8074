import { JobStatus } from './../features/admin/jobs/job-list/data/schema';

declare global {
  interface JobFormData {
    jobTitle: string;
    positionAppliedFor: string;
    location: string;
    minExperience: string;
    maxExperience: string;
    skills: string[];
    startingDate: string;
    endingDate: string;
    description?: string;
    interviewAgent: string;
    interviewDuration: string;
    totalAgents: number;
    totalDuration: string;
  }
  interface JobState {
    currentStep: number;
    formData: JobFormData;
  }

  interface JobActions {
    setCurrentStep: (step: number) => void;
    updateFormData: (data: Partial<JobFormData>) => void;
    nextStep: () => void;
    prevStep: () => void;
    resetForm: () => void;
  }

  type JobSlice = JobState & JobActions;

  export interface JobInformation {
    title: string;
    description?: string;
    position_applied_for: string;
    location: string;
    min_exp: number;
    max_exp: number;
    skills: string[];
    application_start_date: string | undefined;
    application_end_date: string | undefined;
    initial_filter_criteria?: string | undefined;
    status: JobStatus;
    eligibility_settings: {
      inclusive_requirements: string;
      exclusive_requirements: string;
    } | null;
  }

  interface JobInformationResponse extends JobInformation {
    id: number;
  }

  interface JobList {
    id: number;
    title: string;
    location?: string;
    description?: string;
    min_exp?: number;
    max_exp?: number;
    application_start_date?: string;
    application_end_date?: string;
    status: JobStatus;
    eligibility_settings: {
      inclusive_requirements: string;
      exclusive_requirements: string;
    } | null;
  }

  interface ActiveJob {
    id: string;
    title: string;
    location: string;
    min_exp: number;
    max_exp: number;
  }

  enum JOB_STATUS {
    DRAFT = 'draft',
    PUBLISHED = 'published',
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    EXPIRED = 'expired',
  }
}

export {};
