import { ApiServiceInstance, CandidateApiServiceInstance } from '.';
import { API_ROUTES } from '@/config/api';

const { CREATE, GET_ALL, GET_BY_ID, GET_ACTIVE_JOBS } = API_ROUTES.JOB;
export const createJob = async (
  payload: JobInformation
): Promise<IResponseData<JobInformationResponse>> => {
  return ApiServiceInstance.callPostApi<JobInformationResponse, JobInformation>(CREATE, payload);
};
export const getJobs = async (): Promise<IResponseData<JobList[]>> => {
  return ApiServiceInstance.callGetApi<JobList[]>(GET_ALL);
};

export const getActiveJobs = async (): Promise<IResponseData<ActiveJob[]>> => {
  return CandidateApiServiceInstance.callGetApi<ActiveJob[]>(GET_ACTIVE_JOBS);
};

export const getJob = async (id: string): Promise<IResponseData<JobInformationResponse>> => {
  return CandidateApiServiceInstance.callGetApi<JobInformationResponse>(GET_BY_ID(id));
};
