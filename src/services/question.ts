import { QuestionsApiServiceInstance } from '.';
import { API_ROUTES } from '@/config/api';

export interface GenerateQuestionRequest {
  channel_id: string;
  chat_id?: string;
  //remove this later
  prompt_text?: string;
  cv_link?: string;
  last_question_answer?: {
    chat_id?: string | null;
    answer: string;
  };
}

export interface GenerateQuestionResponse {
  success: boolean;
  message?: string;
  channel_id: string;
}

// Generate question via API (response comes through WebSocket)
export const generateQuestionAPI = async (
  payload: GenerateQuestionRequest
): Promise<IResponseData<GenerateQuestionResponse>> => {
  return QuestionsApiServiceInstance.callPostApi<GenerateQuestionResponse, GenerateQuestionRequest>(
    API_ROUTES.QUESTION.GENERATE,
    payload
  );
};
