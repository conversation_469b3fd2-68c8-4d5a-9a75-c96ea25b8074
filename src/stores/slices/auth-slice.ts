import { type StateCreator } from 'zustand';

const initialState: AuthState = {
  access_token: '',
  refresh_token: '',
  user: {
    id: 0,
    email: '',
    user_name: '',
    is_active: false,
  },
};

export const createAuthSlice: StateCreator<
  Store,
  [['zustand/immer', never], ['zustand/devtools', never]],
  [],
  AuthSlice
> = (set) => ({
  ...initialState,
  setAccessToken: (accessToken: string) => set(() => ({ access_token: accessToken })),
  setRefreshToken: (refreshToken: string) => set(() => ({ refresh_token: refreshToken })),
  setAuth: (auth: AuthState) => set(() => ({ ...auth })),
  setUser: (user: AuthState['user']) => set(() => ({ user })),
  updateUser: (user: Partial<AuthState['user']>) =>
    set((state: AuthSlice) => ({ user: { ...state.user, ...user } })),
  clearAuth: () => set(() => initialState),
});
