import { type StateCreator } from 'zustand';

const initialState: DashboardState = {
  selectedJob: null,
  selectedCandidate: null,
};

export const createDashboardSlice: StateCreator<
  Store,
  [['zustand/immer', never], ['zustand/devtools', never]],
  [],
  DashboardSlice
> = (set) => ({
  ...initialState,
  setSelectedJob: (job: DashboardJob | null) => set(() => ({ selectedJob: job })),
  clearSelectedJob: () => set(() => ({ selectedJob: null })),
  setSelectedCandidate: (candidate: CandidateList | null) =>
    set(() => ({ selectedCandidate: candidate })),
  clearSelectedCandidate: () => set(() => ({ selectedCandidate: null })),
});
