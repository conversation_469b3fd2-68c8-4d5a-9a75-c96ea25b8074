import { createDashboardSlice } from './slices/dashboard-slice';
import { createAuthSlice } from '@/stores/slices/auth-slice';
import { createJobSlice } from '@/stores/slices/job-slice';
import { createLanguageSlice } from '@/stores/slices/languag-slice';
import { createThemeSlice } from '@/stores/slices/theme-slice';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export const useStore = create<Store>()(
  devtools(
    immer((...a) => ({
      ...createAuthSlice(...a),
      ...createThemeSlice(...a),
      ...createLanguageSlice(...a),
      ...createDashboardSlice(...a),
      ...createJobSlice(...a),
    })),
    {
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);
