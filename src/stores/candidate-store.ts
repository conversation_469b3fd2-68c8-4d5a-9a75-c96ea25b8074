import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface CandidateState {
  candidateId: string | null;
  candidateDetails: {
    name: string;
    email: string;
    phone: string;
  };
  jobTitle: string | null;
  setCandidateId: (id: string | null) => void;
  setCandidateDetails: (name: string, email: string, phone: string) => void;
  clearCandidateData: () => void;
  setJobTitle: (title: string | null) => void;
  clearJobTitle: () => void;
}

export const useCandidateStore = create<CandidateState>()(
  persist(
    (set) => ({
      candidateId: null,
      jobTitle: null,

      candidateDetails: {
        name: '',
        email: '',
        phone: '',
      },

      setCandidateId: (id: string | null) => set({ candidateId: id }),
      setCandidateDetails: (name: string, email: string, phone: string) =>
        set({ candidateDetails: { name, email, phone } }),
      clearCandidateData: () =>
        set({ candidateId: null, candidateDetails: { name: '', email: '', phone: '' } }),

      setJobTitle: (title: string | null) => set({ jobTitle: title }),
      clearJobTitle: () => set({ jobTitle: null }),
    }),
    {
      name: 'candidate-storage',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
