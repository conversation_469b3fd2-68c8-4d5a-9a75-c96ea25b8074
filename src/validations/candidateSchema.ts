import { z } from 'zod';

const CreateCandidateSchema = z.object({
  full_name: z.string().min(1, 'Full name is required'),
  email: z.string().email('Invalid email address'),
  phone_number: z.string().min(1, 'Phone number is required'),
  address: z.string().min(1, 'Address is required'),
  years_of_experience: z
    .string()
    .min(1, 'Years of experience is required')
    .regex(/^\d+$/, 'Years of experience must be a positive number'),
});

export type CreateCandidateType = z.infer<typeof CreateCandidateSchema>;

export default CreateCandidateSchema;

export const emailVerificationSchema = z.object({
  full_name: z.string().min(1, 'Full name is required').max(35, 'Full name is too long'),
  email: z.string().email('Invalid email address'),
  phone_number: z.string().min(1, 'Phone number is required').max(15, 'Phone number is too long'),
});

export const applicationDetailsSchema = z.object({
  address: z.string().min(1, 'Address is required').max(100, 'Address is too long'),
  years_of_experience: z
    .string({
      required_error: 'Years of experience is required',
      invalid_type_error: 'Years of experience must be provided',
    })
    .min(1, 'Years of experience is required')
    .refine(
      (val) => {
        // Checking valid whole number format only
        return /^-?\d+$/.test(val);
      },
      {
        message: 'Please enter a whole number only (e.g., 1, 3)',
      }
    )
    .transform((val) => parseInt(val, 10))
    .refine((val) => val >= 0, 'Years of experience cannot be negative')
    .refine((val) => val <= 50, 'Years of experience cannot exceed 50 years'),
});

export type EmailVerificationData = z.infer<typeof emailVerificationSchema>;
export type ApplicationDetailsData = z.infer<typeof applicationDetailsSchema>;
