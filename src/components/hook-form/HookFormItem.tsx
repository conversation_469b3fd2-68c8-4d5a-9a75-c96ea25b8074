import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { cn } from '@/lib/utils';
import React, { cloneElement, isValidElement } from 'react';
import { useFormContext } from 'react-hook-form';

interface Props {
  children: React.ReactNode;
  name: string;
  label?: string;
  labelClassName?: string;
  className?: string;
  defaultValue?: string | number;
  isRequired?: boolean;
}

const HookFormItem = ({
  children,
  name,
  label,
  labelClassName,
  className,
  isRequired = false,
}: Props) => {
  const { control } = useFormContext();
  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState: { error } }) => (
        <FormItem className={className}>
          {label && (
            <FormLabel className={cn('font-semibold text-black', labelClassName)}>
              {label}
              {isRequired && <span style={{ color: 'red', marginLeft: '-6px' }}>*</span>}
            </FormLabel>
          )}
          <FormControl>
            {isValidElement(children)
              ? cloneElement(children, {
                  ...field,
                  // @ts-expect-error: children.props.onChange is not guaranteed to exist, but handling it safely GG
                  onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                    field.onChange(event);

                    // if onChange is passed externally, then it will be triggered after hook form's onChange
                    // @ts-expect-error - children.props.onChange might not exist
                    children.props?.onChange?.(event);
                  },
                  error: error?.message,
                })
              : children}
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default HookFormItem;
