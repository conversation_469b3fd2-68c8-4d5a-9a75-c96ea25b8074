'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useLogout } from '@/hooks/api/use-auth';
import { useAuthSlice } from '@/hooks/use-auth-slice';
import { ProfileMenuProps } from '@/types/sidebar';
import { Link } from '@tanstack/react-router';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { User, Headset, LogOut, ChevronDown, BellIcon } from 'lucide-react';

export function ProfileDropdown({ userAvatar, userEmail = '' }: ProfileMenuProps) {
  const { user } = useAuthSlice();
  const logout = useLogout();
  const navigate = useNavigate();

  const userName = user?.user_name || user?.name;
  const displayEmail = userEmail || user?.email || '';

  const initials =
    user?.user_name?.charAt(0)?.toUpperCase() || user?.name?.charAt(0)?.toUpperCase() || 'AP';

  const handleLogout = () => {
    logout();
    navigate({ to: '/login' });
  };

  return (
    <div className='flex items-center gap-2'>
      <motion.div
        whileHover={{
          scale: 1.1,
          rotate: [0, -10, 10, -10, 0],
        }}
        whileTap={{ scale: 0.95 }}
        transition={{
          duration: 0.3,
          ease: 'easeInOut',
          rotate: {
            duration: 0.5,
            ease: 'easeInOut',
          },
        }}
        className='cursor-pointer'
      >
        <BellIcon className='size-5' />
      </motion.div>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant='ghost' className='relative w-auto rounded-full p-0'>
            <div className='flex items-center gap-2'>
              <Avatar className='hover:bg-primary-600 h-8 w-8 rounded-full'>
                <AvatarImage src={userAvatar} alt={userName} />
                <AvatarFallback className='bg-primary hover:bg-primary-600 text-primary-foreground rounded-lg text-xs'>
                  {initials}
                </AvatarFallback>
              </Avatar>
            </div>
            <span className='text-muted-foreground hover:text-foreground text-sm'>{userName}</span>
            <ChevronDown />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className='w-52 rounded-lg' align='end' forceMount sideOffset={4}>
          <DropdownMenuLabel className='p-0 font-normal'>
            <div className='flex items-center gap-2 px-1 py-1.5 text-left text-sm'>
              <Avatar className='h-8 w-8 rounded-full'>
                <AvatarImage src={userAvatar} alt={userName} />
                <AvatarFallback className='bg-primary text-primary-foreground rounded-lg text-xs'>
                  {initials}
                </AvatarFallback>
              </Avatar>
              <div className='grid flex-1 text-left text-sm leading-tight'>
                <span className='truncate font-semibold'>{userName}</span>
                {displayEmail && (
                  <span className='text-muted-foreground truncate text-xs'>{displayEmail}</span>
                )}
              </div>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem className='cursor-pointer' asChild>
              <Link to='/settings'>
                <User className='mr-2 size-5' />
                View Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem className='cursor-pointer' asChild>
              {/* <Link to='/support'> */}
              <Link to='/admin/dashboard'>
                <Headset className='mr-2 size-5' />
                Support
              </Link>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className='text-destructive hover:bg-destructive/10 focus:bg-destructive/10 cursor-pointer'
            onClick={handleLogout}
          >
            <LogOut className='mr-2 size-5' />
            Sign Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
