import { Input } from '../ui';
import { cn } from '@/lib/utils';
import { Search } from 'lucide-react';

interface Props extends React.InputHTMLAttributes<HTMLInputElement> {
  wrapperClassName?: string;
}

export const SearchInput = ({ wrapperClassName, className, ...props }: Props) => {
  return (
    <div className={cn('relative', wrapperClassName)}>
      <Input className={cn('w-70 pl-10', className)} {...props} placeholder='Search' />
      <Search className='text-gray-light absolute top-1/2 left-3 size-5 -translate-y-1/2' />
    </div>
  );
};
