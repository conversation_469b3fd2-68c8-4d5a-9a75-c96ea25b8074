import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

interface Props extends React.ComponentPropsWithoutRef<'div'> {
  icon: React.ReactNode;
  tooltipText: string;
}

const IconTooltipProvider = ({ icon, tooltipText, ...props }: Props) => {
  return (
    <div {...props}>
      <Tooltip>
        <TooltipTrigger asChild>{icon}</TooltipTrigger>
        <TooltipContent>
          <p>{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </div>
  );
};
export default IconTooltipProvider;
