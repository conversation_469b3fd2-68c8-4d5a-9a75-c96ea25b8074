"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Badge } from "@/components/ui/badge"

export interface TabConfig {
  id: string
  label: string
  count?: number
}

interface AnimatedTabsProps {
  tabs: TabConfig[]
  defaultTab?: string
  onTabChange?: (tab: string) => void
  showCounts?: boolean
  className?: string
}

export function AnimatedTabs({ 
  tabs, 
  defaultTab, 
  onTabChange, 
  showCounts = true,
  className = ""
}: AnimatedTabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id || "")

  const handleTabChange = (value: string) => {
    setActiveTab(value)
    onTabChange?.(value)
  }

  return (
    <div className={`w-fit ${className}`}>
      <div className="relative flex items-center bg-white dark:bg-gray-900 rounded-xl p-1 shadow-sm border border-gray-200 dark:border-gray-700">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => handleTabChange(tab.id)}
            className={`
              relative px-4 py-2.5 text-sm font-medium transition-colors duration-200 rounded-lg
              ${
                activeTab === tab.id
                  ? "text-white dark:text-black"
                  : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
              }
            `}
            style={{
              WebkitTapHighlightColor: "transparent",
            }}
          >
            {/* Animated background */}
            {activeTab === tab.id && (
              <motion.div
                layoutId="activeTabBackground"
                className="absolute inset-0 bg-primary rounded-lg shadow-sm"
                transition={{
                  type: "spring",
                  bounce: 0.15,
                  duration: 0.5,
                }}
              />
            )}

            {/* Tab content */}
            <div className="relative flex items-center gap-2">
              <motion.span
                animate={{
                  scale: activeTab === tab.id ? 1.02 : 1,
                }}
                transition={{
                  type: "spring",
                  bounce: 0.2,
                  duration: 0.3,
                }}
              >
                {tab.label}
              </motion.span>

              {showCounts && tab.count !== undefined && (
                <AnimatePresence mode="wait">
                  <motion.div
                    key={`${tab.id}-${tab.count}`}
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.8, opacity: 0 }}
                    transition={{
                      type: "spring",
                      bounce: 0.4,
                      duration: 0.3,
                    }}
                  >
                    <Badge
                      variant={activeTab === tab.id ? "secondary" : "outline"}
                      className={`
                        text-xs font-semibold transition-all duration-200
                        ${
                          activeTab === tab.id
                            ? "bg-white/20 text-white dark:text-black border-white/30 hover:bg-white/30"
                            : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                        }
                      `}
                    >
                      {tab.count}
                    </Badge>
                  </motion.div>
                </AnimatePresence>
              )}
            </div>
          </button>
        ))}
      </div>


    </div>
  )
}