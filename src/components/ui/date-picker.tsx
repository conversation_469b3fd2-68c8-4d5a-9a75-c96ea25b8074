'use client';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import * as React from 'react';

function formatDate(date: Date | undefined) {
  if (!date) {
    return '';
  }

  return date.toLocaleDateString('en-US', {
    day: '2-digit',
    month: 'long',
    year: 'numeric',
  });
}

function isValidDate(date: Date | undefined) {
  if (!date) {
    return false;
  }
  return !isNaN(date.getTime());
}
export interface DatePickerProps {
  defaultDate?: Date | undefined;
  onChange?: (date: Date | undefined) => void;
  label?: string;
  labelClassName?: string;
  id?: string;
}

function DatePicker({
  defaultDate,
  onChange,
  label = '',
  labelClassName = '',
  id = 'date',
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false);
  const [date, setDate] = React.useState<Date | undefined>(defaultDate);
  const [month, setMonth] = React.useState<Date | undefined>(defaultDate ?? new Date());
  const [value, setValue] = React.useState(defaultDate ? formatDate(defaultDate) : '');

  return (
    <div className='flex flex-col gap-3'>
      {label && (
        <Label htmlFor='date' className={labelClassName}>
          {label}
        </Label>
      )}
      <div className='relative flex gap-2'>
        <Input
          id={id}
          value={value}
          readOnly
          placeholder='June 01, 2025'
          className='bg-background pr-10'
          onChange={(e) => {
            const newDate = new Date(e.target.value);
            setValue(e.target.value);
            if (isValidDate(newDate)) {
              setDate(newDate);
              setMonth(newDate);
              onChange?.(newDate);
            }
          }}
          onKeyDown={(e) => {
            if (e.key === 'ArrowDown') {
              e.preventDefault();
              setOpen(true);
            }
          }}
        />
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              id='date-picker'
              variant='ghost'
              className='hover:bg-accent hover:text-accent-foreground absolute top-1/2 right-2 size-6 -translate-y-1/2 p-0 text-gray-700'
            >
              <CalendarIcon className='size-5' />
              <span className='sr-only'>Select date</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className='w-auto overflow-hidden p-0'
            align='end'
            alignOffset={-8}
            sideOffset={10}
          >
            <Calendar
              mode='single'
              selected={date}
              captionLayout='dropdown'
              month={month}
              onMonthChange={setMonth}
              onSelect={(selectedDate) => {
                setDate(selectedDate);
                setValue(formatDate(selectedDate));
                setOpen(false);
                onChange?.(selectedDate);
              }}
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}

export { DatePicker };
