import { cn } from '@/lib/utils';

const DataTableHeader: DataTableHeaderComponent = ({ children, className }) => {
  return (
    <div className={cn('flex items-center justify-between gap-4 p-6', className)}>{children}</div>
  );
};

DataTableHeader.Left = ({ children, className }) => {
  return <div className={cn('flex items-center gap-3', className)}>{children}</div>;
};

DataTableHeader.Right = ({ children, className }) => {
  return <div className={cn('flex items-center gap-2', className)}>{children}</div>;
};

export { DataTableHeader };
