import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import * as React from 'react';

interface Props {
  items: TabItem[];
  defaultTabIndex?: number;
  className?: string;
  onChange?: (index: number) => void;
  showCard?: boolean;
  contentHeight?: string;
}

// const slideVariants = {
//   enter: (direction: number) => ({
//     x: direction > 0 ? '100%' : '-100%',
//     opacity: 0,
//     filter: 'blur(8px)',
//     scale: 0.95,
//     position: 'absolute' as const,
//   }),
//   center: {
//     x: 0,
//     opacity: 1,
//     filter: 'blur(0px)',
//     scale: 1,
//     position: 'absolute' as const,
//   },
//   exit: (direction: number) => ({
//     x: direction < 0 ? '100%' : '-100%',
//     opacity: 0,
//     filter: 'blur(8px)',
//     scale: 0.95,
//     position: 'absolute' as const,
//   }),
// };

// const transition = {
//   duration: 0.4,
//   ease: [0.32, 0.72, 0, 1],
// };

export function DataTabs({
  items,
  defaultTabIndex = 0,
  className,
  onChange,
  contentHeight,
}: Props) {
  const [selected, setSelected] = React.useState<number>(defaultTabIndex);
  const [direction, setDirection] = React.useState(0);
  const [dimensions, setDimensions] = React.useState({ width: 0, left: 0 });

  const buttonRefs = React.useRef<Map<number, HTMLButtonElement>>(new Map());
  const containerRef = React.useRef<HTMLDivElement>(null);

  React.useLayoutEffect(() => {
    const updateDimensions = () => {
      const selectedButton = buttonRefs.current.get(selected);
      const container = containerRef.current;

      if (selectedButton && container) {
        const rect = selectedButton.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        setDimensions({
          width: rect.width,
          left: rect.left - containerRect.left,
        });
      }
    };

    requestAnimationFrame(() => {
      updateDimensions();
    });

    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [selected]);

  const handleTabClick = (index: number) => {
    setDirection(index > selected ? 1 : -1);
    setSelected(index);
    onChange?.(index);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>, index: number) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleTabClick(index);
    }
  };

  const selectedItem = items[selected];

  return (
    <div className='flex h-full flex-col gap-4'>
      <div
        ref={containerRef}
        role='tablist'
        aria-label='tabs'
        className={cn(
          'relative mt-auto flex items-center justify-between gap-1 py-1.5',
          'bg-custom-white mr-auto',
          'rounded-xl border',
          'transition-all duration-200',
          className
        )}
      >
        <motion.div
          className={'bg-primary absolute z-[1] rounded-lg'}
          initial={false}
          animate={{
            width: dimensions.width - 8,
            x: dimensions.left + 4,
            opacity: 1,
          }}
          transition={{
            type: 'spring',
            stiffness: 400,
            damping: 30,
          }}
          style={{ height: 'calc(100% - 8px)', top: '4px' }}
        />

        <div
          className='relative z-[2] w-full gap-1'
          style={{
            display: 'grid',
            gridTemplateColumns: `repeat(${items.length}, 1fr)`,
          }}
        >
          {items.map((item, index) => {
            const isSelected = selected === index;
            return (
              <motion.button
                key={item.value}
                ref={(el) => {
                  if (el) buttonRefs.current.set(index, el);
                  else buttonRefs.current.delete(index);
                }}
                type='button'
                role='tab'
                aria-selected={isSelected}
                aria-controls={`panel-${index}`}
                id={`tab-${index}`}
                tabIndex={isSelected ? 0 : -1}
                onClick={() => handleTabClick(index)}
                onKeyDown={(e) => handleKeyDown(e, index)}
                className={cn(
                  'relative flex items-center justify-center rounded-lg px-4 py-1.5',
                  'text-sm font-medium transition-all duration-300',
                  'focus-visible:ring-ring focus-visible:ring-2 focus-visible:outline-none',
                  'truncate',
                  isSelected
                    ? 'text-white'
                    : 'text-gray-dark hover:bg-muted/50 hover:text-foreground'
                )}
              >
                {item.icon && <item.icon className='h-4 w-4' />}
                <span className='truncate'>{item.label}</span>
                {item.count && (
                  <span className='bg-muted text-muted-foreground ml-1 rounded-full px-1.5 py-0.5 text-xs'>
                    {item.count}
                  </span>
                )}
              </motion.button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className='relative flex-1'>
        <div
          className='border-strock relative h-full w-full rounded-2xl border'
          style={{ height: contentHeight ?? '400px' }}
        >
          <div className='absolute inset-0 overflow-hidden rounded-2xl'>
            <AnimatePresence initial={false} mode='popLayout' custom={direction}>
              <motion.div
                key={`card-${selected}`}
                // custom={direction}
                // variants={slideVariants as any}
                // initial='enter'
                // animate='center'
                // exit='exit'
                // transition={transition as any}
                className='bg-card absolute inset-0 h-full w-full will-change-transform'
                style={{
                  backfaceVisibility: 'hidden',
                  WebkitBackfaceVisibility: 'hidden',
                }}
              >
                {selectedItem?.content()}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
}
