import UploadCloud from '@/assets/icons/upload-cloud';
import { Loader } from '@/components/shared/loader';
import { FileText } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useDropzone, type Accept } from 'react-dropzone';
import { toast } from 'sonner';

interface Props {
  placeholder?: string;
  supportedFormats?: string;
  isMultiple?: boolean;
  onFilesUploaded: (files: File[]) => void;
  acceptedFileTypes: Accept;
  placeHolder2?: string;
  isLoading?: boolean;
  clearFiles?: boolean;
  maxFileSize?: number; // in MB
}

const FileUploader = ({
  placeholder,
  placeHolder2,
  supportedFormats,
  isMultiple,
  onFilesUploaded,
  acceptedFileTypes,
  isLoading,
  clearFiles,
  maxFileSize,
}: Props) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  useEffect(() => {
    if (clearFiles) {
      setUploadedFile(null);
    }
  }, [clearFiles]);

  const onDrop = (acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      if (maxFileSize && acceptedFiles[0].size > maxFileSize) {
        toast.error(
          `File size exceeds ${maxFileSize / 1024 / 1024} MB. Please upload a smaller file.`
        );
        return;
      }

      setUploadedFile(acceptedFiles[0]);
      onFilesUploaded(acceptedFiles);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes,
    multiple: isMultiple ?? false,
  });

  return (
    <div
      {...getRootProps()}
      className={`border-primary cursor-pointer rounded-md border-2 border-dashed p-8 text-center transition-colors ${
        isDragActive
          ? 'border-primary bg-primary/10'
          : 'border-muted-foreground/25 hover:border-primary'
      }`}
    >
      <input {...getInputProps()} />
      {isLoading ? (
        <div className='flex items-center justify-center space-x-2'>
          <Loader size='sm' />
          <span className='text-gray-dark ml-2 animate-pulse'>Uploading in progress...</span>
        </div>
      ) : uploadedFile ? (
        <div className='flex items-center justify-center space-x-2'>
          <FileText className='text-primary size-6' />
          <span>{uploadedFile.name}</span>
        </div>
      ) : (
        <div>
          <UploadCloud className='mx-auto mb-2' />
          <p className='font-medium text-black'>
            {placeholder ?? 'Drag & drop your files here'}
            {placeHolder2 && <span className='text-primary'> {placeHolder2}</span>}
          </p>
          {supportedFormats && <p className='text-gray-light mt-1 text-sm'>{supportedFormats}</p>}
        </div>
      )}
    </div>
  );
};

export default FileUploader;
