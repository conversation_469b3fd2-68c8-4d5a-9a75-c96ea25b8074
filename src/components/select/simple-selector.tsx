import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { forwardRef } from 'react';

interface Props {
  options: SelectProp[] | [];
  label?: string;
  error?: string;
  placeholder?: string;
  className?: string;
  name?: string;
  defaultValue?: string;
  onChange?: (val: string | number) => void;
  value?: string;
}

const SimpleSelect = forwardRef<HTMLDivElement, Props>(
  ({ options, className, name, label, error, placeholder, value, onChange }, ref) => {
    return (
      <div>
        {label && (
          <label htmlFor={label} className='text-gray-dark mb-1.5 block text-sm font-medium'>
            {label}
          </label>
        )}
        <Select value={value} onValueChange={onChange} name={name}>
          <SelectTrigger
            className={cn(
              'w-full',
              {
                'border-destructive focus-visible:border-input focus-visible:ring-destructive':
                  error,
                'focus-ring border-input focus-visible:border-input': !value,
              },
              className
            )}
          >
            <SelectValue placeholder={placeholder ?? 'Select'} />
          </SelectTrigger>
          <SelectContent ref={ref} className='z-[100]'>
            {options.map((option: SelectProp) => (
              <SelectItem value={option.value} key={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }
);

SimpleSelect.displayName = 'SimpleSelect';

export default SimpleSelect;
