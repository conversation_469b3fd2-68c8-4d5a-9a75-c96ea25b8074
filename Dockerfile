# ---------- Base stage ----------
FROM node:18-alpine AS base

# Install pnpm
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy dependency files
COPY package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy all source code
COPY . .

# ---------- Build stage ----------
FROM base AS build

COPY app.env .env

# Build the app
RUN pnpm run build

# ---------- Runtime stage ----------
FROM node:18-alpine AS runtime

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy build artifacts and package files
COPY --from=build /app /app

# Expose port 3000
EXPOSE 3000

# Run Vite preview on host 0.0.0.0:3000
CMD ["pnpm", "run", "preview"]

